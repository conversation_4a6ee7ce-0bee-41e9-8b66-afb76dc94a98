# Dashboard API 快速参考

## 基本信息
- **服务器**: `http://*************:5001`
- **方法**: GET
- **格式**: JSON（一层对象）
- **错误**: `{"error": "错误信息"}`

## 接口列表

| 接口 | 路径 | 功能 |
|------|------|------|
| 生产概览 | `/api/dashboard/overview/{line}` | 当日生产概览 |
| 7天趋势 | `/api/dashboard/trend/{line}` | 最近7天趋势 |
| 每小时生产 | `/api/dashboard/hourly/{line}` | 今日24小时数据 |
| 质量分析 | `/api/dashboard/quality/{line}` | 质量统计分析 |
| 每小时效率 | `/api/dashboard/efficiency/{line}` | 今日效率数据 |
| 故障排行 | `/api/dashboard/malfunction/{line}?days=7` | 故障类型排行 |
| 所有数据 | `/api/dashboard/all/{line}` | 合并所有数据 |

## 主要字段

### 生产概览
```json
{
  "product_id": "300-596-205",
  "total_production": 850,
  "production_efficiency": 85.0,
  "qualified_rate": 95.2,
  "error_rate": 4.8,
  "target_production": 1000,
  "actual_malfunctions": 42
}
```

### 7天趋势
```json
{
  "dates": ["2025-08-17", "...", "2025-08-23"],
  "production_counts": [120, 135, 142, 128, 156, 134, 130],
  "total_7days": 945,
  "avg_daily": 135.0
}
```

### 每小时生产
```json
{
  "hours": ["00:00", "01:00", "...", "23:00"],
  "production_counts": [0, 0, 15, 18, "..."],
  "current_hour": 14,
  "current_hour_production": 28,
  "today_total": 350
}
```

### 质量分析
```json
{
  "qualified_products": 850,
  "defective_products": 42,
  "qualified_rate": 95.29,
  "defect_rate": 4.71,
  "quality_grade": "优秀"
}
```

### 每小时效率
```json
{
  "hours": ["00:00", "01:00", "...", "23:00"],
  "production_counts": [0, 0, 15, 18, "..."],
  "efficiency_rates": [0.0, 0.0, 35.7, 42.9, "..."],
  "target_hourly": 42,
  "avg_efficiency": 45.2,
  "current_hour_efficiency": 66.7
}
```

### 故障排行
```json
{
  "malfunction_types": ["传感器故障", "机械卡顿", "电路异常"],
  "malfunction_counts": [15, 12, 8],
  "malfunction_percentages": [35.7, 28.6, 19.0],
  "total_malfunctions": 42,
  "top_malfunction": "传感器故障"
}
```

## 使用示例

### JavaScript
```javascript
// 获取生产概览
fetch('http://*************:5001/api/dashboard/overview/1')
  .then(res => res.json())
  .then(data => {
    console.log('产量:', data.total_production);
    console.log('效率:', data.production_efficiency + '%');
  });

// 获取所有数据
fetch('http://*************:5001/api/dashboard/all/1')
  .then(res => res.json())
  .then(data => {
    // 直接访问字段，无需嵌套
    console.log('今日产量:', data.total_production);
    console.log('7天趋势:', data.trend_production_counts);
    console.log('质量等级:', data.quality_quality_grade);
  });
```

### Python
```python
import requests

# 获取数据
url = 'http://*************:5001/api/dashboard/overview/1'
data = requests.get(url).json()

if 'error' in data:
    print(f"错误: {data['error']}")
else:
    print(f"产量: {data['total_production']}")
    print(f"效率: {data['production_efficiency']}%")
```

### cURL
```bash
# 生产概览
curl "http://*************:5001/api/dashboard/overview/1"

# 所有数据
curl "http://*************:5001/api/dashboard/all/1"

# 故障排行（指定天数）
curl "http://*************:5001/api/dashboard/malfunction/1?days=7"
```

## 数据字段前缀（all接口）

当使用 `/api/dashboard/all/{line}` 接口时，不同模块的字段使用前缀区分：

- **无前缀**: 生产概览数据
- **trend_**: 7天趋势数据
- **hourly_**: 每小时生产数据  
- **quality_**: 质量分析数据
- **efficiency_**: 每小时效率数据
- **ranking_**: 故障排行数据

例如：
```json
{
  "total_production": 850,           // 生产概览
  "trend_total_7days": 945,          // 7天趋势
  "hourly_today_total": 350,         // 每小时生产
  "quality_qualified_rate": 95.29,   // 质量分析
  "efficiency_avg_efficiency": 45.2, // 每小时效率
  "ranking_top_malfunction": "传感器故障" // 故障排行
}
```

## 注意事项

1. **生产线参数**: 支持1和2，路径中指定
2. **响应格式**: 直接返回数据对象，无success/data嵌套
3. **错误处理**: 错误时返回 `{"error": "错误信息"}`
4. **数据实时性**: 数据来自MySQL数据库，实时更新
5. **时间格式**: 使用 `YYYY-MM-DD HH:MM:SS` 格式
