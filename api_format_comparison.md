# Dashboard API 格式简化对比

## 修改前的格式（嵌套结构）

### 生产概览接口
```json
{
  "success": true,
  "data": {
    "product_id": "300-596-205",
    "total_production": 850,
    "production_efficiency": 85.0,
    "avg_cycle_time": 35.4,
    "qualified_rate": 95.2,
    "error_rate": 4.8,
    "target_production": 1000,
    "actual_malfunctions": 42,
    "update_time": "2025-08-23 14:30:00"
  }
}
```

### 7天趋势接口
```json
{
  "success": true,
  "data": {
    "dates": ["2025-08-17", "2025-08-18", "..."],
    "production_counts": [120, 135, 142, "..."],
    "malfunction_counts": [5, 8, 3, "..."],
    "total_7day_production": 945,
    "total_7day_malfunctions": 38,
    "avg_daily_production": 135.0,
    "avg_daily_malfunctions": 5.4
  }
}
```

## 修改后的格式（一层对象）

### 生产概览接口
```json
{
  "product_id": "300-596-205",
  "total_production": 850,
  "production_efficiency": 85.0,
  "avg_cycle_time": 35.4,
  "qualified_rate": 95.2,
  "error_rate": 4.8,
  "target_production": 1000,
  "actual_malfunctions": 42,
  "update_time": "2025-08-23 14:30:00"
}
```

### 7天趋势接口
```json
{
  "dates": ["2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23"],
  "production_counts": [120, 135, 142, 128, 156, 134, 130],
  "total_7days": 945,
  "avg_daily": 135.0
}
```

### 每小时生产数据接口
```json
{
  "hours": ["00:00", "01:00", "02:00", "..."],
  "production_counts": [0, 0, 0, 15, 18, "..."],
  "current_hour": 14,
  "current_hour_production": 25,
  "today_total": 350
}
```

### 质量分析接口
```json
{
  "qualified_products": 850,
  "defective_products": 42,
  "total_products": 892,
  "qualified_rate": 95.29,
  "defect_rate": 4.71,
  "quality_grade": "优秀",
  "period": "2025-08-17 至 2025-08-23"
}
```

### 每小时效率接口
```json
{
  "hours": ["00:00", "01:00", "02:00", "..."],
  "production_counts": [0, 0, 0, 15, 18, "..."],
  "efficiency_rates": [0.0, 0.0, 0.0, 35.7, 42.9, "..."],
  "target_hourly": 42,
  "avg_efficiency": 65.5,
  "current_hour": 14,
  "current_hour_efficiency": 59.5
}
```

### 故障排行接口
```json
{
  "malfunction_types": ["传感器故障", "机械卡顿", "电路异常", "..."],
  "malfunction_counts": [15, 12, 8, 5, 2],
  "malfunction_percentages": [35.7, 28.6, 19.0, 11.9, 4.8],
  "occurrence_days": [7, 6, 5, 3, 2],
  "avg_daily_counts": [2.1, 2.0, 1.6, 1.7, 1.0],
  "total_malfunctions": 42,
  "period_days": 7,
  "period": "2025-08-17 至 2025-08-23",
  "top_malfunction": "传感器故障"
}
```

### 所有看板数据接口
```json
{
  "line_number": 1,
  "update_time": "2025-08-23 14:30:00",
  "product_id": "300-596-205",
  "total_production": 850,
  "production_efficiency": 85.0,
  "avg_cycle_time": 35.4,
  "qualified_rate": 95.2,
  "error_rate": 4.8,
  "target_production": 1000,
  "actual_malfunctions": 42,
  "trend_dates": ["2025-08-17", "2025-08-18", "..."],
  "trend_production_counts": [120, 135, 142, "..."],
  "trend_total_7days": 945,
  "trend_avg_daily": 135.0,
  "hourly_hours": ["00:00", "01:00", "..."],
  "hourly_production_counts": [0, 0, 0, 15, "..."],
  "hourly_current_hour": 14,
  "hourly_current_hour_production": 25,
  "hourly_today_total": 350,
  "quality_qualified_products": 850,
  "quality_defective_products": 42,
  "quality_total_products": 892,
  "quality_qualified_rate": 95.29,
  "quality_defect_rate": 4.71,
  "quality_quality_grade": "优秀",
  "quality_period": "2025-08-17 至 2025-08-23",
  "efficiency_hours": ["00:00", "01:00", "..."],
  "efficiency_production_counts": [0, 0, 0, 15, "..."],
  "efficiency_efficiency_rates": [0.0, 0.0, 0.0, 35.7, "..."],
  "efficiency_target_hourly": 42,
  "efficiency_avg_efficiency": 65.5,
  "efficiency_current_hour": 14,
  "efficiency_current_hour_efficiency": 59.5,
  "ranking_malfunction_types": ["传感器故障", "机械卡顿", "..."],
  "ranking_malfunction_counts": [15, 12, 8, 5, 2],
  "ranking_malfunction_percentages": [35.7, 28.6, 19.0, 11.9, 4.8],
  "ranking_occurrence_days": [7, 6, 5, 3, 2],
  "ranking_avg_daily_counts": [2.1, 2.0, 1.6, 1.7, 1.0],
  "ranking_total_malfunctions": 42,
  "ranking_period_days": 7,
  "ranking_period": "2025-08-17 至 2025-08-23",
  "ranking_top_malfunction": "传感器故障"
}
```

## 主要改进

1. **去除嵌套结构**: 不再使用 `{"success": true, "data": {...}}` 格式
2. **扁平化数据**: 所有数据字段都在根级别
3. **简化错误处理**: 错误时直接返回 `{"error": "错误信息"}`
4. **统一格式**: 所有接口都使用相同的扁平化格式
5. **前端友好**: 减少了前端解析数据时的嵌套访问

## 错误响应格式

修改前：
```json
{
  "success": false,
  "error": "数据库未连接"
}
```

修改后：
```json
{
  "error": "数据库未连接"
}
```
