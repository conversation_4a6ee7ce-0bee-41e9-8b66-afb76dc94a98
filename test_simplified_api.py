#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的Dashboard API
验证所有接口都返回一层JSON对象
"""

import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://*************:5001"

def test_api_endpoint(endpoint, description):
    """测试单个API端点"""
    print(f"\n=== 测试 {description} ===")
    print(f"URL: {BASE_URL}{endpoint}")
    
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据类型: {type(data)}")
            
            # 检查是否为一层对象
            if isinstance(data, dict):
                print(f"字段数量: {len(data)}")
                print("主要字段:")
                for key, value in list(data.items())[:10]:  # 只显示前10个字段
                    value_type = type(value).__name__
                    if isinstance(value, list):
                        value_preview = f"[{len(value)} items]"
                    elif isinstance(value, str) and len(value) > 50:
                        value_preview = f"{value[:50]}..."
                    else:
                        value_preview = str(value)
                    print(f"  {key}: {value_preview} ({value_type})")
                
                if len(data) > 10:
                    print(f"  ... 还有 {len(data) - 10} 个字段")
                    
                # 检查是否有嵌套的success/data结构
                if "success" in data and "data" in data:
                    print("⚠️  警告: 仍然包含success/data嵌套结构")
                else:
                    print("✅ 确认: 返回一层JSON对象")
            else:
                print(f"❌ 错误: 返回的不是对象，而是 {type(data)}")
                
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("Dashboard API 简化测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试所有API端点
    endpoints = [
        ("/api/dashboard/overview/1", "生产概览"),
        ("/api/dashboard/trend/1", "7天生产趋势"),
        ("/api/dashboard/hourly/1", "每小时生产数据"),
        ("/api/dashboard/quality/1", "质量分析"),
        ("/api/dashboard/efficiency/1", "每小时效率"),
        ("/api/dashboard/malfunction/1", "故障排行"),
        ("/api/dashboard/all/1", "所有看板数据")
    ]
    
    for endpoint, description in endpoints:
        test_api_endpoint(endpoint, description)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
