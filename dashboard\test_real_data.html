<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时数据测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-label {
            font-weight: bold;
            color: #2c3e50;
        }
        .status-value {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status-success {
            background: #27ae60;
            color: white;
        }
        .status-error {
            background: #e74c3c;
            color: white;
        }
        .status-warning {
            background: #f39c12;
            color: white;
        }
        .log-panel {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #2980b9; }
        .data-preview {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .data-content {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Dashboard 实时数据测试</h1>
            <p>验证Dashboard是否正确使用API数据（无模拟数据）</p>
            <button class="btn" onclick="startTest()">开始测试</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div class="status-panel">
            <h3>连接状态</h3>
            <div class="status-item">
                <span class="status-label">API服务器</span>
                <span class="status-value status-warning" id="api-status">未测试</span>
            </div>
            <div class="status-item">
                <span class="status-label">合并接口 (/all)</span>
                <span class="status-value status-warning" id="all-status">未测试</span>
            </div>
            <div class="status-item">
                <span class="status-label">分离接口</span>
                <span class="status-value status-warning" id="separate-status">未测试</span>
            </div>
            <div class="status-item">
                <span class="status-label">数据完整性</span>
                <span class="status-value status-warning" id="data-status">未测试</span>
            </div>
            <div class="status-item">
                <span class="status-label">最后更新</span>
                <span class="status-value" id="last-update">-</span>
            </div>
        </div>

        <div class="data-preview">
            <div class="data-title">生产概览数据</div>
            <div class="data-content" id="overview-data">等待数据...</div>
        </div>

        <div class="data-preview">
            <div class="data-title">趋势数据</div>
            <div class="data-content" id="trend-data">等待数据...</div>
        </div>

        <div class="data-preview">
            <div class="data-title">质量数据</div>
            <div class="data-content" id="quality-data">等待数据...</div>
        </div>

        <div class="status-panel">
            <h3>测试日志</h3>
            <div class="log-panel" id="log-panel">点击"开始测试"查看详细日志...</div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://*************:5001/api/dashboard';
        const LINE_NUMBER = 1;
        let logContent = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}`;
            
            logContent += logMessage + '\n';
            document.getElementById('log-panel').textContent = logContent;
            document.getElementById('log-panel').scrollTop = document.getElementById('log-panel').scrollHeight;
            
            console.log(logMessage);
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status-value status-${status}`;
        }

        function updateDataPreview(elementId, data) {
            const element = document.getElementById(elementId);
            if (data && typeof data === 'object') {
                element.textContent = JSON.stringify(data, null, 2);
            } else {
                element.textContent = data || '无数据';
            }
        }

        async function testApiEndpoint(endpoint, description) {
            try {
                log(`测试 ${description}: ${endpoint}`);
                const response = await fetch(`${API_BASE_URL}${endpoint}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                log(`${description} 成功`, 'success');
                return data;
                
            } catch (error) {
                log(`${description} 失败: ${error.message}`, 'error');
                return null;
            }
        }

        async function startTest() {
            log('开始测试Dashboard API数据获取...', 'info');
            updateStatus('last-update', 'warning', '测试中...');
            
            // 测试合并接口
            log('=== 测试合并接口 ===');
            const allData = await testApiEndpoint(`/all/${LINE_NUMBER}`, '合并接口');
            
            if (allData) {
                updateStatus('all-status', 'success', '正常');
                updateStatus('api-status', 'success', '连接正常');
                
                // 分析数据完整性
                const hasOverview = allData.product_id !== undefined;
                const hasTrend = allData.trend_dates && allData.trend_production_counts;
                const hasQuality = allData.quality_qualified_rate !== undefined;
                const hasEfficiency = allData.efficiency_avg_efficiency !== undefined;
                
                log(`数据完整性检查:`);
                log(`- 生产概览: ${hasOverview ? '✅' : '❌'}`);
                log(`- 趋势数据: ${hasTrend ? '✅' : '❌'}`);
                log(`- 质量数据: ${hasQuality ? '✅' : '❌'}`);
                log(`- 效率数据: ${hasEfficiency ? '✅' : '❌'}`);
                
                const dataComplete = hasOverview && hasTrend && hasQuality && hasEfficiency;
                updateStatus('data-status', dataComplete ? 'success' : 'warning', 
                           dataComplete ? '完整' : '部分缺失');
                
                // 更新数据预览
                updateDataPreview('overview-data', {
                    product_id: allData.product_id,
                    total_production: allData.total_production,
                    production_efficiency: allData.production_efficiency,
                    qualified_rate: allData.quality_qualified_rate
                });
                
                updateDataPreview('trend-data', {
                    dates: allData.trend_dates,
                    production_counts: allData.trend_production_counts,
                    total_7days: allData.trend_total_7days
                });
                
                updateDataPreview('quality-data', {
                    qualified_rate: allData.quality_qualified_rate,
                    defect_rate: allData.quality_defect_rate,
                    total_products: allData.quality_total_products
                });
                
            } else {
                updateStatus('all-status', 'error', '失败');
                
                // 测试分离接口
                log('=== 测试分离接口 ===');
                const [overview, trend, quality] = await Promise.all([
                    testApiEndpoint(`/overview/${LINE_NUMBER}`, '生产概览'),
                    testApiEndpoint(`/trend/${LINE_NUMBER}`, '趋势数据'),
                    testApiEndpoint(`/quality/${LINE_NUMBER}`, '质量数据')
                ]);
                
                const separateSuccess = overview || trend || quality;
                updateStatus('separate-status', separateSuccess ? 'success' : 'error', 
                           separateSuccess ? '部分正常' : '全部失败');
                
                if (separateSuccess) {
                    updateStatus('api-status', 'warning', '部分连接');
                    updateDataPreview('overview-data', overview);
                    updateDataPreview('trend-data', trend);
                    updateDataPreview('quality-data', quality);
                } else {
                    updateStatus('api-status', 'error', '连接失败');
                    updateStatus('data-status', 'error', '无数据');
                }
            }
            
            updateStatus('last-update', 'success', new Date().toLocaleTimeString());
            log('测试完成', 'success');
        }

        function clearLog() {
            logContent = '';
            document.getElementById('log-panel').textContent = '日志已清空...';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，准备测试API连接');
            log(`API基础URL: ${API_BASE_URL}`);
            log(`生产线编号: ${LINE_NUMBER}`);
        });
    </script>
</body>
</html>
