<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard API 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .api-section {
            background: white;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .api-header {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        .api-content {
            padding: 20px;
        }
        .api-url {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .api-result {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #27ae60; color: white; }
        .status.error { background: #e74c3c; color: white; }
        .status.loading { background: #f39c12; color: white; }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #2980b9; }
        .btn:disabled { background: #bdc3c7; cursor: not-allowed; }
        .summary {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Dashboard API 测试页面</h1>
            <p>测试简化后的Dashboard API接口连接和数据格式</p>
            <div>
                <button class="btn" onclick="testAllApis()">测试所有接口</button>
                <button class="btn" onclick="clearResults()">清空结果</button>
            </div>
        </div>

        <div class="summary" id="summary">
            <h3>测试摘要</h3>
            <p>点击"测试所有接口"开始测试</p>
        </div>

        <div class="api-section">
            <div class="api-header">
                生产概览 <span class="status loading" id="status-overview">未测试</span>
            </div>
            <div class="api-content">
                <div class="api-url">GET /api/dashboard/overview/1</div>
                <button class="btn" onclick="testApi('overview', '/overview/1')">测试此接口</button>
                <div class="api-result" id="result-overview">等待测试...</div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-header">
                7天生产趋势 <span class="status loading" id="status-trend">未测试</span>
            </div>
            <div class="api-content">
                <div class="api-url">GET /api/dashboard/trend/1</div>
                <button class="btn" onclick="testApi('trend', '/trend/1')">测试此接口</button>
                <div class="api-result" id="result-trend">等待测试...</div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-header">
                每小时生产数据 <span class="status loading" id="status-hourly">未测试</span>
            </div>
            <div class="api-content">
                <div class="api-url">GET /api/dashboard/hourly/1</div>
                <button class="btn" onclick="testApi('hourly', '/hourly/1')">测试此接口</button>
                <div class="api-result" id="result-hourly">等待测试...</div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-header">
                质量分析 <span class="status loading" id="status-quality">未测试</span>
            </div>
            <div class="api-content">
                <div class="api-url">GET /api/dashboard/quality/1</div>
                <button class="btn" onclick="testApi('quality', '/quality/1')">测试此接口</button>
                <div class="api-result" id="result-quality">等待测试...</div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-header">
                每小时效率 <span class="status loading" id="status-efficiency">未测试</span>
            </div>
            <div class="api-content">
                <div class="api-url">GET /api/dashboard/efficiency/1</div>
                <button class="btn" onclick="testApi('efficiency', '/efficiency/1')">测试此接口</button>
                <div class="api-result" id="result-efficiency">等待测试...</div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-header">
                故障排行 <span class="status loading" id="status-malfunction">未测试</span>
            </div>
            <div class="api-content">
                <div class="api-url">GET /api/dashboard/malfunction/1?days=7</div>
                <button class="btn" onclick="testApi('malfunction', '/malfunction/1?days=7')">测试此接口</button>
                <div class="api-result" id="result-malfunction">等待测试...</div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-header">
                所有看板数据 <span class="status loading" id="status-all">未测试</span>
            </div>
            <div class="api-content">
                <div class="api-url">GET /api/dashboard/all/1</div>
                <button class="btn" onclick="testApi('all', '/all/1')">测试此接口</button>
                <div class="api-result" id="result-all">等待测试...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://*************:5001/api/dashboard';
        const testResults = {};

        async function testApi(name, endpoint) {
            const statusEl = document.getElementById(`status-${name}`);
            const resultEl = document.getElementById(`result-${name}`);
            
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';
            resultEl.textContent = '正在请求数据...';

            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE_URL}${endpoint}`);
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                const data = await response.json();

                if (response.ok && !data.error) {
                    statusEl.textContent = `成功 (${responseTime}ms)`;
                    statusEl.className = 'status success';
                    
                    // 分析数据结构
                    const analysis = analyzeData(data);
                    
                    resultEl.textContent = `响应时间: ${responseTime}ms
状态码: ${response.status}
数据类型: ${typeof data}
字段数量: ${Object.keys(data).length}

数据结构分析:
${analysis}

完整响应数据:
${JSON.stringify(data, null, 2)}`;

                    testResults[name] = { success: true, responseTime, data, analysis };
                } else {
                    throw new Error(data.error || `HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.textContent = `失败: ${error.message}`;
                statusEl.className = 'status error';
                resultEl.textContent = `错误: ${error.message}`;
                testResults[name] = { success: false, error: error.message };
            }

            updateSummary();
        }

        function analyzeData(data) {
            const analysis = [];
            
            // 检查是否为简化格式
            if (data.hasOwnProperty('success') && data.hasOwnProperty('data')) {
                analysis.push('❌ 仍使用嵌套格式 (success/data)');
            } else {
                analysis.push('✅ 使用简化格式 (一层对象)');
            }

            // 分析主要字段
            const keys = Object.keys(data);
            analysis.push(`主要字段: ${keys.slice(0, 10).join(', ')}${keys.length > 10 ? '...' : ''}`);

            // 检查数组字段
            const arrayFields = keys.filter(key => Array.isArray(data[key]));
            if (arrayFields.length > 0) {
                analysis.push(`数组字段: ${arrayFields.join(', ')}`);
            }

            return analysis.join('\n');
        }

        async function testAllApis() {
            const apis = [
                ['overview', '/overview/1'],
                ['trend', '/trend/1'],
                ['hourly', '/hourly/1'],
                ['quality', '/quality/1'],
                ['efficiency', '/efficiency/1'],
                ['malfunction', '/malfunction/1?days=7'],
                ['all', '/all/1']
            ];

            for (const [name, endpoint] of apis) {
                await testApi(name, endpoint);
                await new Promise(resolve => setTimeout(resolve, 500)); // 间隔500ms
            }
        }

        function clearResults() {
            const apis = ['overview', 'trend', 'hourly', 'quality', 'efficiency', 'malfunction', 'all'];
            
            apis.forEach(name => {
                document.getElementById(`status-${name}`).textContent = '未测试';
                document.getElementById(`status-${name}`).className = 'status loading';
                document.getElementById(`result-${name}`).textContent = '等待测试...';
            });

            Object.keys(testResults).forEach(key => delete testResults[key]);
            updateSummary();
        }

        function updateSummary() {
            const summaryEl = document.getElementById('summary');
            const total = Object.keys(testResults).length;
            const successful = Object.values(testResults).filter(r => r.success).length;
            const failed = total - successful;

            if (total === 0) {
                summaryEl.innerHTML = '<h3>测试摘要</h3><p>点击"测试所有接口"开始测试</p>';
                return;
            }

            const avgResponseTime = successful > 0 
                ? Math.round(Object.values(testResults)
                    .filter(r => r.success)
                    .reduce((sum, r) => sum + r.responseTime, 0) / successful)
                : 0;

            summaryEl.innerHTML = `
                <h3>测试摘要</h3>
                <p><strong>总接口数:</strong> ${total}</p>
                <p><strong>成功:</strong> <span style="color: #27ae60">${successful}</span></p>
                <p><strong>失败:</strong> <span style="color: #e74c3c">${failed}</span></p>
                <p><strong>成功率:</strong> ${total > 0 ? Math.round(successful / total * 100) : 0}%</p>
                <p><strong>平均响应时间:</strong> ${avgResponseTime}ms</p>
                <p><strong>测试时间:</strong> ${new Date().toLocaleString()}</p>
            `;
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard API 测试页面已加载');
            console.log('API基础URL:', API_BASE_URL);
        });
    </script>
</body>
</html>
