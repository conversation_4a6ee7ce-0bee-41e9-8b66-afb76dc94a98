# 生产看板Dashboard - API集成版

## 概述

这是一个集成了简化Dashboard API的生产看板系统，实时显示生产数据、质量分析、效率统计和故障排行等信息。

## 文件说明

- `index.html` - 主要的生产看板页面（仅使用API数据）
- `test_api.html` - API接口测试页面
- `test_real_data.html` - 实时数据验证页面
- `README.md` - 本说明文档

## API集成

### API服务器配置

- **服务器地址**: `http://*************:5001`
- **API基础路径**: `/api/dashboard`
- **默认生产线**: 1

### 支持的API接口

1. **生产概览**: `/api/dashboard/overview/1`
2. **7天趋势**: `/api/dashboard/trend/1`
3. **每小时生产**: `/api/dashboard/hourly/1`
4. **质量分析**: `/api/dashboard/quality/1`
5. **每小时效率**: `/api/dashboard/efficiency/1`
6. **故障排行**: `/api/dashboard/malfunction/1?days=7`
7. **所有数据**: `/api/dashboard/all/1`

### 数据格式

所有API接口都返回简化的一层JSON对象格式：

```json
{
  "field1": "value1",
  "field2": 123,
  "field3": [1, 2, 3]
}
```

错误时返回：
```json
{
  "error": "错误描述"
}
```

## 功能特性

### 实时数据显示

- **纯API数据**: 完全使用API获取的真实数据，不使用任何模拟数据
- **自动刷新**: 每30秒自动更新数据
- **连接状态**: 显示API连接状态（实时数据/连接失败）
- **实时时钟**: 显示当前时间
- **错误处理**: API失败时显示错误信息而非模拟数据

### 数据面板

1. **生产数据总览**
   - 产品编号、总生产数量
   - 生产效率、平均生产时间
   - 合格数、错误数

2. **7天生产数量趋势**
   - 柱状图显示最近7天生产数量
   - 支持数据标签显示

3. **每小时生产数据**
   - 折线图显示今日24小时生产数据
   - 双Y轴显示生产数量和错误数

4. **质量分析**
   - KPI卡片显示关键指标
   - 今日PASS率、NG率
   - 平均生产效率、平均生产时间

5. **每小时生产效率**
   - 折线图显示今日效率变化
   - 效率百分比显示

6. **坏机原因排行**
   - 横向柱状图显示故障类型
   - 文本列表显示详细排行

### 容错机制

- **API失败处理**: 当API无法访问时显示错误信息，不使用模拟数据
- **数据验证**: 对API返回数据进行严格验证和处理
- **错误提示**: 在控制台和界面显示详细的错误信息
- **智能重试**: 初始加载失败时停止定时刷新，避免无效请求

## 使用方法

### 1. 直接访问

打开浏览器访问 `index.html` 文件即可查看生产看板。

### 2. API测试

- 访问 `test_api.html` 可以测试所有API接口的连接状态和数据格式
- 访问 `test_real_data.html` 可以验证实时数据获取和处理流程

### 3. 配置修改

如需修改API服务器地址或生产线编号，请编辑 `index.html` 中的配置：

```javascript
const API_BASE_URL = 'http://*************:5001/api/dashboard';
const LINE_NUMBER = 1; // 修改生产线编号
```

## 开发说明

### 数据流程

1. **数据获取**: 优先尝试获取 `/all/1` 合并接口数据
2. **备用方案**: 如果合并接口失败，分别调用各个单独接口
3. **错误处理**: 如果所有API都失败，显示错误信息并停止刷新
4. **数据验证**: 对API返回数据进行严格验证
5. **数据处理**: 将API数据转换为图表所需格式
6. **界面更新**: 更新所有图表和数据显示

### 关键函数

- `fetchAllData()`: 获取所有API数据
- `processNewApiData()`: 处理合并API数据
- `processNewApiDataSeparate()`: 处理分离API数据
- `updateConnectionStatus()`: 更新连接状态显示

### 图表库

使用 Chart.js 进行数据可视化，支持：
- 柱状图（7天趋势、故障排行）
- 折线图（每小时数据、效率图）
- 自定义样式和动画

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查服务器地址是否正确
   - 确认API服务是否正在运行
   - 查看浏览器控制台错误信息

2. **数据显示异常**
   - 检查API返回的数据格式
   - 使用测试页面验证接口状态
   - 查看控制台日志

3. **图表不显示**
   - 确认Chart.js库是否正确加载
   - 检查数据是否为空或格式错误

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console标签页的日志信息
3. 查看Network标签页的API请求状态
4. 使用 `test_api.html` 测试各个接口

## 更新日志

### v2.1 (2025-08-23)
- **移除模拟数据**: 完全使用API真实数据
- **增强错误处理**: API失败时显示错误信息而非模拟数据
- **优化刷新机制**: 初始加载失败时停止定时刷新
- **添加数据验证**: 严格验证API返回数据
- **新增测试页面**: `test_real_data.html` 验证实时数据流程

### v2.0 (2025-08-23)
- 集成简化的Dashboard API
- 支持实时数据获取和显示
- 添加连接状态指示器
- 优化错误处理和容错机制
- 添加API测试页面

### v1.0
- 基础看板界面
- 模拟数据显示
- 图表可视化功能
