#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产线多维度数据获取工具
支持按生产线、时间范围、产品ID、故障类型等多个维度查询数据
"""

import mysql.connector
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any, Union
import json


class LineDataRetrieval:
    """生产线数据多维度查询类"""

    def __init__(self, host: str, port: int, user: str, password: str, database: str):
        """初始化数据库配置"""
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None

    def connect(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False

    def disconnect(self):
        """断开数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()

    def get_production_data(self, 
                          line_number: Optional[int] = None,
                          product_id: Optional[str] = None,
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None,
                          granularity: str = 'daily',
                          limit: int = 100) -> Dict[str, Any]:
        """
        获取生产数据
        
        Args:
            line_number: 生产线编号 (1 或 2)
            product_id: 产品ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            granularity: 数据粒度 ('daily', 'hourly', 'total')
            limit: 返回记录数限制
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # 根据粒度选择表
            if granularity == 'daily':
                table_suffix = 'production_daily_count'
                time_field = 'date_timestamp'
            elif granularity == 'hourly':
                table_suffix = 'production_hourly_count'
                time_field = 'hour_timestamp'
            elif granularity == 'total':
                table_suffix = 'production_count'
                time_field = 'last_updated'
            else:
                return {"success": False, "error": "无效的数据粒度，请使用 'daily', 'hourly' 或 'total'"}

            results = []
            
            # 查询指定生产线或所有生产线
            lines_to_query = [line_number] if line_number else [1, 2]
            
            for line in lines_to_query:
                table_name = f"line{line}_{table_suffix}"
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if product_id:
                    where_conditions.append("product_id = %s")
                    params.append(product_id)
                
                if start_date:
                    where_conditions.append(f"{time_field} >= %s")
                    params.append(start_date)
                
                if end_date:
                    where_conditions.append(f"{time_field} <= %s")
                    params.append(end_date)
                
                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
                
                query = f"""
                    SELECT *, {line} as line_number
                    FROM {table_name}
                    {where_clause}
                    ORDER BY {time_field} DESC
                    LIMIT %s
                """
                params.append(limit)
                
                cursor.execute(query, params)
                line_results = cursor.fetchall()
                results.extend(line_results)
            
            cursor.close()
            
            return {
                "success": True,
                "data": results,
                "total_count": len(results),
                "granularity": granularity,
                "query_params": {
                    "line_number": line_number,
                    "product_id": product_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "limit": limit
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_malfunction_data(self,
                           line_number: Optional[int] = None,
                           product_id: Optional[str] = None,
                           malfunction_type: Optional[str] = None,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           granularity: str = 'daily',
                           limit: int = 100) -> Dict[str, Any]:
        """
        获取故障数据
        
        Args:
            line_number: 生产线编号 (1 或 2)
            product_id: 产品ID
            malfunction_type: 故障类型 (A-J)
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            granularity: 数据粒度 ('daily', 'hourly', 'raw')
            limit: 返回记录数限制
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # 根据粒度选择表
            if granularity == 'daily':
                table_suffix = 'malfunction_daily_count'
                time_field = 'date_timestamp'
            elif granularity == 'hourly':
                table_suffix = 'malfunction_hourly_count'
                time_field = 'hour_timestamp'
            elif granularity == 'raw':
                table_suffix = 'malfunction_raw_data'
                time_field = 'timestamp'
            else:
                return {"success": False, "error": "无效的数据粒度，请使用 'daily', 'hourly' 或 'raw'"}

            results = []
            
            # 查询指定生产线或所有生产线
            lines_to_query = [line_number] if line_number else [1, 2]
            
            for line in lines_to_query:
                table_name = f"line{line}_{table_suffix}"
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if product_id:
                    where_conditions.append("product_id = %s")
                    params.append(product_id)
                
                if malfunction_type:
                    if granularity == 'raw':
                        where_conditions.append("status = %s")
                    else:
                        where_conditions.append("malfunction_type = %s")
                    params.append(malfunction_type)
                
                if start_date:
                    where_conditions.append(f"{time_field} >= %s")
                    params.append(start_date)
                
                if end_date:
                    where_conditions.append(f"{time_field} <= %s")
                    params.append(end_date)
                
                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
                
                query = f"""
                    SELECT *, {line} as line_number
                    FROM {table_name}
                    {where_clause}
                    ORDER BY {time_field} DESC
                    LIMIT %s
                """
                params.append(limit)
                
                cursor.execute(query, params)
                line_results = cursor.fetchall()
                results.extend(line_results)
            
            cursor.close()
            
            return {
                "success": True,
                "data": results,
                "total_count": len(results),
                "granularity": granularity,
                "query_params": {
                    "line_number": line_number,
                    "product_id": product_id,
                    "malfunction_type": malfunction_type,
                    "start_date": start_date,
                    "end_date": end_date,
                    "limit": limit
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_production_messages(self,
                              line_number: Optional[int] = None,
                              topic: Optional[str] = None,
                              product_id: Optional[str] = None,
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None,
                              limit: int = 100) -> Dict[str, Any]:
        """
        获取生产消息日志
        
        Args:
            line_number: 生产线编号 (1 或 2)
            topic: 消息主题
            product_id: 产品ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            limit: 返回记录数限制
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)
            results = []
            
            # 查询指定生产线或所有生产线
            lines_to_query = [line_number] if line_number else [1, 2]
            
            for line in lines_to_query:
                table_name = f"line{line}_production_message_log"
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if topic:
                    where_conditions.append("topic = %s")
                    params.append(topic)
                
                if product_id:
                    where_conditions.append("product_id = %s")
                    params.append(product_id)
                
                if start_date:
                    where_conditions.append("received_at >= %s")
                    params.append(start_date)
                
                if end_date:
                    where_conditions.append("received_at <= %s")
                    params.append(end_date)
                
                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
                
                query = f"""
                    SELECT *, {line} as line_number
                    FROM {table_name}
                    {where_clause}
                    ORDER BY received_at DESC
                    LIMIT %s
                """
                params.append(limit)
                
                cursor.execute(query, params)
                line_results = cursor.fetchall()
                results.extend(line_results)
            
            cursor.close()
            
            return {
                "success": True,
                "data": results,
                "total_count": len(results),
                "query_params": {
                    "line_number": line_number,
                    "topic": topic,
                    "product_id": product_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "limit": limit
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_malfunction_statistics(self,
                                 line_number: Optional[int] = None,
                                 product_id: Optional[str] = None,
                                 start_date: Optional[str] = None,
                                 end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        获取故障统计数据

        Args:
            line_number: 生产线编号 (1 或 2)
            product_id: 产品ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)
            results = {}

            # 查询指定生产线或所有生产线
            lines_to_query = [line_number] if line_number else [1, 2]

            for line in lines_to_query:
                # 构建查询条件
                where_conditions = []
                params = []

                if product_id:
                    where_conditions.append("product_id = %s")
                    params.append(product_id)

                if start_date:
                    where_conditions.append("date_timestamp >= %s")
                    params.append(start_date)

                if end_date:
                    where_conditions.append("date_timestamp <= %s")
                    params.append(end_date)

                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

                # 按故障类型统计
                query = f"""
                    SELECT
                        malfunction_type,
                        SUM(count) as total_count,
                        COUNT(*) as occurrence_days,
                        AVG(count) as avg_daily_count,
                        MAX(count) as max_daily_count,
                        MIN(count) as min_daily_count
                    FROM line{line}_malfunction_daily_count
                    {where_clause}
                    GROUP BY malfunction_type
                    ORDER BY total_count DESC
                """

                cursor.execute(query, params)
                malfunction_stats = cursor.fetchall()

                # 获取故障类型信息
                cursor.execute(f"SELECT type_code, type_name FROM line{line}_malfunction_type_info")
                type_info = {row['type_code']: row['type_name'] for row in cursor.fetchall()}

                # 添加故障类型名称
                for stat in malfunction_stats:
                    stat['type_name'] = type_info.get(stat['malfunction_type'], '未知')

                results[f'line_{line}'] = {
                    'malfunction_statistics': malfunction_stats,
                    'total_malfunctions': sum(stat['total_count'] for stat in malfunction_stats)
                }

            cursor.close()

            return {
                "success": True,
                "data": results,
                "query_params": {
                    "line_number": line_number,
                    "product_id": product_id,
                    "start_date": start_date,
                    "end_date": end_date
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_production_efficiency(self,
                                line_number: Optional[int] = None,
                                product_id: Optional[str] = None,
                                start_date: Optional[str] = None,
                                end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        获取生产效率数据（生产数量 vs 故障数量）

        Args:
            line_number: 生产线编号 (1 或 2)
            product_id: 产品ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)
            results = {}

            # 查询指定生产线或所有生产线
            lines_to_query = [line_number] if line_number else [1, 2]

            for line in lines_to_query:
                # 构建查询条件
                where_conditions = []
                params = []

                if product_id:
                    where_conditions.append("p.product_id = %s")
                    params.append(product_id)

                if start_date:
                    where_conditions.append("p.date_timestamp >= %s")
                    params.append(start_date)

                if end_date:
                    where_conditions.append("p.date_timestamp <= %s")
                    params.append(end_date)

                where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

                # 联合查询生产数据和故障数据
                query = f"""
                    SELECT
                        p.date_timestamp,
                        p.product_id,
                        p.count as production_count,
                        COALESCE(m.malfunction_count, 0) as malfunction_count,
                        CASE
                            WHEN COALESCE(m.malfunction_count, 0) = 0 THEN NULL
                            ELSE ROUND(p.count / COALESCE(m.malfunction_count, 1), 2)
                        END as efficiency_ratio
                    FROM line{line}_production_daily_count p
                    LEFT JOIN (
                        SELECT
                            date_timestamp,
                            product_id,
                            SUM(count) as malfunction_count
                        FROM line{line}_malfunction_daily_count
                        GROUP BY date_timestamp, product_id
                    ) m ON p.date_timestamp = m.date_timestamp
                        AND p.product_id = m.product_id
                    {where_clause}
                    ORDER BY p.date_timestamp DESC
                """

                cursor.execute(query, params)
                efficiency_data = cursor.fetchall()

                # 计算总体统计
                total_production = sum(row['production_count'] for row in efficiency_data)
                total_malfunctions = sum(row['malfunction_count'] for row in efficiency_data)
                avg_efficiency = (total_production / total_malfunctions) if total_malfunctions > 0 else None

                results[f'line_{line}'] = {
                    'daily_efficiency': efficiency_data,
                    'summary': {
                        'total_production': total_production,
                        'total_malfunctions': total_malfunctions,
                        'overall_efficiency_ratio': avg_efficiency,
                        'days_count': len(efficiency_data)
                    }
                }

            cursor.close()

            return {
                "success": True,
                "data": results,
                "query_params": {
                    "line_number": line_number,
                    "product_id": product_id,
                    "start_date": start_date,
                    "end_date": end_date
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_line_comparison(self,
                          product_id: Optional[str] = None,
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None,
                          metric: str = 'production') -> Dict[str, Any]:
        """
        获取生产线对比数据

        Args:
            product_id: 产品ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            metric: 对比指标 ('production', 'malfunction', 'efficiency')
        """
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)

            if metric == 'production':
                # 生产数量对比
                line1_data = self.get_production_data(1, product_id, start_date, end_date, 'daily')
                line2_data = self.get_production_data(2, product_id, start_date, end_date, 'daily')

                line1_total = sum(item['count'] for item in line1_data['data']) if line1_data['success'] else 0
                line2_total = sum(item['count'] for item in line2_data['data']) if line2_data['success'] else 0

                return {
                    "success": True,
                    "metric": "production",
                    "comparison": {
                        "line_1": {
                            "total_production": line1_total,
                            "daily_data": line1_data['data'] if line1_data['success'] else []
                        },
                        "line_2": {
                            "total_production": line2_total,
                            "daily_data": line2_data['data'] if line2_data['success'] else []
                        },
                        "difference": line1_total - line2_total,
                        "ratio": (line1_total / line2_total) if line2_total > 0 else None
                    }
                }

            elif metric == 'malfunction':
                # 故障数量对比
                line1_stats = self.get_malfunction_statistics(1, product_id, start_date, end_date)
                line2_stats = self.get_malfunction_statistics(2, product_id, start_date, end_date)

                line1_total = line1_stats['data'].get('line_1', {}).get('total_malfunctions', 0) if line1_stats['success'] else 0
                line2_total = line2_stats['data'].get('line_2', {}).get('total_malfunctions', 0) if line2_stats['success'] else 0

                return {
                    "success": True,
                    "metric": "malfunction",
                    "comparison": {
                        "line_1": {
                            "total_malfunctions": line1_total,
                            "statistics": line1_stats['data'].get('line_1', {}) if line1_stats['success'] else {}
                        },
                        "line_2": {
                            "total_malfunctions": line2_total,
                            "statistics": line2_stats['data'].get('line_2', {}) if line2_stats['success'] else {}
                        },
                        "difference": line1_total - line2_total,
                        "ratio": (line1_total / line2_total) if line2_total > 0 else None
                    }
                }

            else:
                return {"success": False, "error": f"不支持的对比指标: {metric}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_time_range_summary(self,
                             line_number: Optional[int] = None,
                             product_id: Optional[str] = None,
                             days: int = 7) -> Dict[str, Any]:
        """
        获取指定天数内的汇总数据

        Args:
            line_number: 生产线编号 (1 或 2)
            product_id: 产品ID
            days: 查询天数（默认7天）
        """
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        try:
            # 获取生产数据
            production_data = self.get_production_data(line_number, product_id, start_date, end_date, 'daily')

            # 获取故障数据
            malfunction_data = self.get_malfunction_statistics(line_number, product_id, start_date, end_date)

            # 获取效率数据
            efficiency_data = self.get_production_efficiency(line_number, product_id, start_date, end_date)

            return {
                "success": True,
                "time_range": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "days": days
                },
                "summary": {
                    "production": production_data if production_data['success'] else {},
                    "malfunction": malfunction_data if malfunction_data['success'] else {},
                    "efficiency": efficiency_data if efficiency_data['success'] else {}
                },
                "query_params": {
                    "line_number": line_number,
                    "product_id": product_id
                }
            }

        except Exception as e:
            return {"success": False, "error": str(e)}


# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': '20016f7d55f95fb9',
    'database': 'production_data'
}


def print_json_result(result: Dict[str, Any], title: str = "查询结果"):
    """格式化打印JSON结果"""
    print(f"\n=== {title} ===")
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    print("=" * (len(title) + 8))


def demo_usage():
    """演示各种查询功能"""
    # 创建数据检索实例
    retriever = LineDataRetrieval(**DB_CONFIG)

    if not retriever.connect():
        print("数据库连接失败！")
        return

    try:
        print("=== 生产线多维度数据查询演示 ===\n")

        # 1. 查询最近7天的生产数据
        print("1. 查询Line1最近7天的生产数据（按日统计）")
        result = retriever.get_production_data(
            line_number=1,
            granularity='daily',
            limit=7
        )
        print_json_result(result, "Line1 最近7天生产数据")

        # 2. 查询特定产品的故障统计
        print("\n2. 查询特定产品的故障统计")
        result = retriever.get_malfunction_statistics(
            line_number=1,
            product_id='300-596-203'
        )
        print_json_result(result, "Line1 故障统计")

        # 3. 查询生产效率数据
        print("\n3. 查询生产效率数据")
        result = retriever.get_production_efficiency(
            line_number=1,
            start_date='2025-08-20',
            end_date='2025-08-23'
        )
        print_json_result(result, "生产效率数据")

        # 4. 生产线对比
        print("\n4. 生产线生产数量对比")
        result = retriever.get_line_comparison(
            metric='production',
            start_date='2025-08-20',
            end_date='2025-08-23'
        )
        print_json_result(result, "生产线对比")

        # 5. 最近7天汇总数据
        print("\n5. 最近7天汇总数据")
        result = retriever.get_time_range_summary(
            line_number=1,
            days=7
        )
        print_json_result(result, "最近7天汇总")

        # 6. 查询生产消息日志
        print("\n6. 查询生产消息日志")
        result = retriever.get_production_messages(
            line_number=1,
            topic='factory/line1/counter',
            limit=5
        )
        print_json_result(result, "生产消息日志")

    finally:
        retriever.disconnect()


if __name__ == '__main__':
    print("生产线多维度数据查询工具")
    print("支持的查询维度：")
    print("- 生产线编号 (1 或 2)")
    print("- 产品ID")
    print("- 时间范围")
    print("- 故障类型")
    print("- 数据粒度 (daily/hourly/raw)")
    print("\n支持的查询类型：")
    print("- 生产数据查询")
    print("- 故障数据查询")
    print("- 生产消息日志查询")
    print("- 故障统计分析")
    print("- 生产效率分析")
    print("- 生产线对比分析")
    print("- 时间范围汇总")

    # 运行演示
    demo_usage()
