# 生产看板数据API接口文档

## 基本信息

- **服务器地址**: `http://*************:5001`
- **API版本**: v1.0
- **数据格式**: JSON
- **字符编码**: UTF-8
- **更新时间**: 2025-08-23

## 通用说明

### 请求格式
- **方法**: GET
- **Content-Type**: application/json

### 响应格式
- **成功响应**: 直接返回数据对象（一层JSON）
- **错误响应**: `{"error": "错误描述"}`
- **HTTP状态码**: 200（成功）、500（服务器错误）

### 生产线参数
- `line_number`: 生产线编号，支持1和2
- 所有接口都支持通过URL路径参数指定生产线

---

## 接口列表

### 1. 生产概览

**接口地址**: `GET /api/dashboard/overview/{line_number}`

**功能描述**: 获取指定生产线的当日生产概览数据

**请求示例**:
```
GET /api/dashboard/overview/1
```

**响应示例**:
```json
{
  "product_id": "300-596-205",
  "total_production": 850,
  "production_efficiency": 85.0,
  "avg_cycle_time": 35.4,
  "qualified_rate": 95.2,
  "error_rate": 4.8,
  "target_production": 1000,
  "actual_malfunctions": 42,
  "update_time": "2025-08-23 14:30:00"
}
```

**字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| product_id | string | 当前产品编号 |
| total_production | integer | 今日总生产数量 |
| production_efficiency | float | 生产效率百分比 |
| avg_cycle_time | float | 平均生产周期 |
| qualified_rate | float | 合格率百分比 |
| error_rate | float | 错误率百分比 |
| target_production | integer | 目标产量 |
| actual_malfunctions | integer | 实际故障数量 |
| update_time | string | 数据更新时间 |

---

### 2. 7天生产趋势

**接口地址**: `GET /api/dashboard/trend/{line_number}`

**功能描述**: 获取最近7天的生产数量趋势数据

**请求示例**:
```
GET /api/dashboard/trend/1
```

**响应示例**:
```json
{
  "dates": ["2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23"],
  "production_counts": [120, 135, 142, 128, 156, 134, 130],
  "total_7days": 945,
  "avg_daily": 135.0
}
```

**字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| dates | array | 日期数组（7天） |
| production_counts | array | 对应日期的生产数量 |
| total_7days | integer | 7天总生产数量 |
| avg_daily | float | 日均生产数量 |

---

### 3. 每小时生产数据

**接口地址**: `GET /api/dashboard/hourly/{line_number}`

**功能描述**: 获取今日24小时的生产数据

**请求示例**:
```
GET /api/dashboard/hourly/1
```

**响应示例**:
```json
{
  "hours": ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"],
  "production_counts": [0, 0, 0, 0, 0, 0, 15, 18, 22, 25, 28, 30, 32, 25, 28, 30, 26, 24, 20, 15, 10, 5, 0, 0],
  "current_hour": 14,
  "current_hour_production": 28,
  "today_total": 350
}
```

**字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| hours | array | 24小时时间标签 |
| production_counts | array | 对应小时的生产数量 |
| current_hour | integer | 当前小时（0-23） |
| current_hour_production | integer | 当前小时生产数量 |
| today_total | integer | 今日总生产数量 |

---

### 4. 质量分析

**接口地址**: `GET /api/dashboard/quality/{line_number}`

**功能描述**: 获取最近7天的质量分析数据

**请求示例**:
```
GET /api/dashboard/quality/1
```

**响应示例**:
```json
{
  "qualified_products": 850,
  "defective_products": 42,
  "total_products": 892,
  "qualified_rate": 95.29,
  "defect_rate": 4.71,
  "quality_grade": "优秀",
  "period": "2025-08-17 至 2025-08-23"
}
```

**字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| qualified_products | integer | 合格产品数量 |
| defective_products | integer | 不合格产品数量 |
| total_products | integer | 总产品数量 |
| qualified_rate | float | 合格率百分比 |
| defect_rate | float | 不合格率百分比 |
| quality_grade | string | 质量等级（优秀/良好/一般） |
| period | string | 统计周期 |

---

### 5. 每小时效率

**接口地址**: `GET /api/dashboard/efficiency/{line_number}`

**功能描述**: 获取今日24小时的生产效率数据

**请求示例**:
```
GET /api/dashboard/efficiency/1
```

**响应示例**:
```json
{
  "hours": ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"],
  "production_counts": [0, 0, 0, 0, 0, 0, 15, 18, 22, 25, 28, 30, 32, 25, 28, 30, 26, 24, 20, 15, 10, 5, 0, 0],
  "efficiency_rates": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 35.7, 42.9, 52.4, 59.5, 66.7, 71.4, 76.2, 59.5, 66.7, 71.4, 61.9, 57.1, 47.6, 35.7, 23.8, 11.9, 0.0, 0.0],
  "target_hourly": 42,
  "avg_efficiency": 45.2,
  "current_hour": 14,
  "current_hour_efficiency": 66.7
}
```

**字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| hours | array | 24小时时间标签 |
| production_counts | array | 对应小时的生产数量 |
| efficiency_rates | array | 对应小时的效率百分比 |
| target_hourly | integer | 每小时目标产量 |
| avg_efficiency | float | 平均效率百分比 |
| current_hour | integer | 当前小时 |
| current_hour_efficiency | float | 当前小时效率 |

---

### 6. 故障排行

**接口地址**: `GET /api/dashboard/malfunction/{line_number}?days={days}`

**功能描述**: 获取指定天数内的故障类型排行数据

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| days | integer | 否 | 7 | 统计天数 |

**请求示例**:
```
GET /api/dashboard/malfunction/1?days=7
```

**响应示例**:
```json
{
  "malfunction_types": ["传感器故障", "机械卡顿", "电路异常", "温度过高", "压力异常"],
  "malfunction_counts": [15, 12, 8, 5, 2],
  "malfunction_percentages": [35.7, 28.6, 19.0, 11.9, 4.8],
  "occurrence_days": [7, 6, 5, 3, 2],
  "avg_daily_counts": [2.1, 2.0, 1.6, 1.7, 1.0],
  "total_malfunctions": 42,
  "period_days": 7,
  "period": "2025-08-17 至 2025-08-23",
  "top_malfunction": "传感器故障"
}
```

**字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| malfunction_types | array | 故障类型名称数组 |
| malfunction_counts | array | 对应故障类型的数量 |
| malfunction_percentages | array | 对应故障类型的百分比 |
| occurrence_days | array | 对应故障类型的发生天数 |
| avg_daily_counts | array | 对应故障类型的日均数量 |
| total_malfunctions | integer | 总故障数量 |
| period_days | integer | 统计天数 |
| period | string | 统计周期 |
| top_malfunction | string | 排名第一的故障类型 |

---

### 7. 所有看板数据

**接口地址**: `GET /api/dashboard/all/{line_number}`

**功能描述**: 一次性获取所有看板模块的数据，合并到一个扁平化对象中

**请求示例**:
```
GET /api/dashboard/all/1
```

**响应示例**:
```json
{
  "line_number": 1,
  "update_time": "2025-08-23 14:30:00",
  "product_id": "300-596-205",
  "total_production": 850,
  "production_efficiency": 85.0,
  "avg_cycle_time": 35.4,
  "qualified_rate": 95.2,
  "error_rate": 4.8,
  "target_production": 1000,
  "actual_malfunctions": 42,
  "trend_dates": ["2025-08-17", "2025-08-18", "2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23"],
  "trend_production_counts": [120, 135, 142, 128, 156, 134, 130],
  "trend_total_7days": 945,
  "trend_avg_daily": 135.0,
  "hourly_hours": ["00:00", "01:00", "02:00", "..."],
  "hourly_production_counts": [0, 0, 0, 15, 18, "..."],
  "hourly_current_hour": 14,
  "hourly_current_hour_production": 28,
  "hourly_today_total": 350,
  "quality_qualified_products": 850,
  "quality_defective_products": 42,
  "quality_total_products": 892,
  "quality_qualified_rate": 95.29,
  "quality_defect_rate": 4.71,
  "quality_quality_grade": "优秀",
  "quality_period": "2025-08-17 至 2025-08-23",
  "efficiency_hours": ["00:00", "01:00", "02:00", "..."],
  "efficiency_production_counts": [0, 0, 0, 15, 18, "..."],
  "efficiency_efficiency_rates": [0.0, 0.0, 0.0, 35.7, 42.9, "..."],
  "efficiency_target_hourly": 42,
  "efficiency_avg_efficiency": 45.2,
  "efficiency_current_hour": 14,
  "efficiency_current_hour_efficiency": 66.7,
  "ranking_malfunction_types": ["传感器故障", "机械卡顿", "电路异常", "温度过高", "压力异常"],
  "ranking_malfunction_counts": [15, 12, 8, 5, 2],
  "ranking_malfunction_percentages": [35.7, 28.6, 19.0, 11.9, 4.8],
  "ranking_occurrence_days": [7, 6, 5, 3, 2],
  "ranking_avg_daily_counts": [2.1, 2.0, 1.6, 1.7, 1.0],
  "ranking_total_malfunctions": 42,
  "ranking_period_days": 7,
  "ranking_period": "2025-08-17 至 2025-08-23",
  "ranking_top_malfunction": "传感器故障"
}
```

**字段说明**:
此接口合并了所有其他接口的数据，字段名使用模块前缀区分：
- 无前缀：生产概览数据
- `trend_`：7天趋势数据
- `hourly_`：每小时生产数据
- `quality_`：质量分析数据
- `efficiency_`：每小时效率数据
- `ranking_`：故障排行数据

---

## 错误处理

### 错误响应格式
```json
{
  "error": "错误描述信息"
}
```

### 常见错误
| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| "数据库未连接" | 数据库连接失败 | 检查数据库服务状态 |
| "生产线编号必须是1或2" | 无效的生产线编号 | 使用正确的生产线编号 |
| "接口不存在" | 请求的接口路径错误 | 检查API路径是否正确 |

---

## 使用示例

### JavaScript/Fetch
```javascript
// 获取生产概览
fetch('http://*************:5001/api/dashboard/overview/1')
  .then(response => response.json())
  .then(data => {
    if (data.error) {
      console.error('错误:', data.error);
    } else {
      console.log('产品ID:', data.product_id);
      console.log('生产数量:', data.total_production);
      console.log('生产效率:', data.production_efficiency + '%');
    }
  });

// 获取所有数据
fetch('http://*************:5001/api/dashboard/all/1')
  .then(response => response.json())
  .then(data => {
    if (data.error) {
      console.error('错误:', data.error);
    } else {
      // 直接访问所有数据字段
      console.log('生产线:', data.line_number);
      console.log('今日产量:', data.total_production);
      console.log('7天趋势:', data.trend_production_counts);
      console.log('质量等级:', data.quality_quality_grade);
    }
  });
```

### Python/Requests
```python
import requests

# 获取生产概览
response = requests.get('http://*************:5001/api/dashboard/overview/1')
data = response.json()

if 'error' in data:
    print(f"错误: {data['error']}")
else:
    print(f"产品ID: {data['product_id']}")
    print(f"生产数量: {data['total_production']}")
    print(f"生产效率: {data['production_efficiency']}%")

# 获取故障排行
response = requests.get('http://*************:5001/api/dashboard/malfunction/1?days=7')
data = response.json()

if 'error' in data:
    print(f"错误: {data['error']}")
else:
    print(f"故障类型: {data['malfunction_types']}")
    print(f"故障数量: {data['malfunction_counts']}")
    print(f"最多故障: {data['top_malfunction']}")
```

### cURL
```bash
# 获取生产概览
curl -X GET "http://*************:5001/api/dashboard/overview/1"

# 获取7天趋势
curl -X GET "http://*************:5001/api/dashboard/trend/1"

# 获取故障排行（指定天数）
curl -X GET "http://*************:5001/api/dashboard/malfunction/1?days=7"

# 获取所有数据
curl -X GET "http://*************:5001/api/dashboard/all/1"
```

---

## 更新日志

### v1.0 (2025-08-23)
- 简化API响应格式，移除嵌套的success/data结构
- 所有接口返回扁平化的一层JSON对象
- 统一错误响应格式
- 优化数据结构，便于前端处理

