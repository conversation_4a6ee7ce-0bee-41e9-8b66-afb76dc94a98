{"info": {"name": "Dashboard API Collection", "description": "生产看板数据API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://*************:5001", "type": "string"}, {"key": "lineNumber", "value": "1", "type": "string"}], "item": [{"name": "生产概览", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/overview/{{lineNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "overview", "{{lineNumber}}"]}, "description": "获取指定生产线的当日生产概览数据"}, "response": []}, {"name": "7天生产趋势", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/trend/{{lineNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "trend", "{{lineNumber}}"]}, "description": "获取最近7天的生产数量趋势数据"}, "response": []}, {"name": "每小时生产数据", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/hourly/{{lineNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "hourly", "{{lineNumber}}"]}, "description": "获取今日24小时的生产数据"}, "response": []}, {"name": "质量分析", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/quality/{{lineNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "quality", "{{lineNumber}}"]}, "description": "获取最近7天的质量分析数据"}, "response": []}, {"name": "每小时效率", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/efficiency/{{lineNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "efficiency", "{{lineNumber}}"]}, "description": "获取今日24小时的生产效率数据"}, "response": []}, {"name": "故障排行", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/malfunction/{{lineNumber}}?days=7", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "malfunction", "{{lineNumber}}"], "query": [{"key": "days", "value": "7", "description": "统计天数"}]}, "description": "获取指定天数内的故障类型排行数据"}, "response": []}, {"name": "所有看板数据", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/all/{{lineNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "all", "{{lineNumber}}"]}, "description": "一次性获取所有看板模块的数据"}, "response": []}, {"name": "生产线2 - 生产概览", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/overview/2", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "overview", "2"]}, "description": "获取生产线2的生产概览数据"}, "response": []}, {"name": "生产线2 - 所有数据", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/all/2", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "all", "2"]}, "description": "获取生产线2的所有看板数据"}, "response": []}, {"name": "故障排行 - 30天", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/malfunction/{{lineNumber}}?days=30", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "malfunction", "{{lineNumber}}"], "query": [{"key": "days", "value": "30", "description": "统计30天的故障数据"}]}, "description": "获取最近30天的故障类型排行数据"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 设置请求时间戳", "pm.globals.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 通用测试脚本", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is JSON', function () {", "    pm.response.to.be.json;", "});", "", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// 检查是否为简化格式（一层对象）", "pm.test('Response is flat object (no success/data nesting)', function () {", "    const jsonData = pm.response.json();", "    ", "    // 如果有错误，应该是 {error: 'message'} 格式", "    if (jsonData.error) {", "        pm.expect(jsonData).to.have.property('error');", "        pm.expect(typeof jsonData.error).to.equal('string');", "    } else {", "        // 成功响应不应该有 success 和 data 字段", "        pm.expect(jsonData).to.not.have.property('success');", "        pm.expect(jsonData).to.not.have.property('data');", "        ", "        // 应该直接包含数据字段", "        pm.expect(Object.keys(jsonData).length).to.be.above(0);", "    }", "});", "", "// 打印响应数据结构", "pm.test('Log response structure', function () {", "    const jsonData = pm.response.json();", "    console.log('Response keys:', Object.keys(jsonData));", "    console.log('Response type:', typeof jsonData);", "});"]}}]}