#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产看板数据接口
为飞控子工序生产看板提供所需的数据接口
"""

import mysql.connector
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json


class DashboardDataAPI:
    """生产看板数据API类"""

    def __init__(self, db_config: Dict[str, Any]):
        """初始化"""
        self.config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'user': db_config['user'],
            'password': db_config['password'],
            'database': db_config['database'],
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.connect()

    def connect(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
    
    def get_production_overview(self, line_number: int = 1) -> Dict[str, Any]:
        """
        获取生产概览总览数据
        包含：产品编号、总生产数量、生产效率、平均生产周期、合格率、错误率
        """
        if not self.connection or not self.connection.is_connected():
            return {"error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)

            # 获取当前产品信息
            cursor.execute("SELECT product_id FROM production_lines WHERE line_number = %s", (line_number,))
            result = cursor.fetchone()
            current_product = result['product_id'] if result else 'N/A'

            # 获取今日数据
            today = datetime.now().strftime('%Y-%m-%d')

            # 今日生产数量
            cursor.execute(f"""
                SELECT COALESCE(SUM(count), 0) as total_production
                FROM line{line_number}_production_daily_count
                WHERE date_timestamp = %s
            """, (today,))
            result = cursor.fetchone()
            today_production = result['total_production'] if result else 0

            # 今日故障数量
            cursor.execute(f"""
                SELECT COALESCE(SUM(count), 0) as total_malfunctions
                FROM line{line_number}_malfunction_daily_count
                WHERE date_timestamp = %s
            """, (today,))
            result = cursor.fetchone()
            today_malfunctions = result['total_malfunctions'] if result else 0

            # 计算合格率和错误率
            total_products = today_production + today_malfunctions
            qualified_rate = (today_production / total_products * 100) if total_products > 0 else 100
            error_rate = (today_malfunctions / total_products * 100) if total_products > 0 else 0

            # 生产效率（假设目标产量为1000）
            target_production = 1000
            efficiency = (today_production / target_production * 100) if target_production > 0 else 0

            # 平均生产周期（假设每小时平均产量）
            current_hour = datetime.now().hour
            avg_cycle_time = (today_production / current_hour) if current_hour > 0 else 0

            cursor.close()

            return {
                "product_id": current_product,
                "total_production": today_production,
                "production_efficiency": round(efficiency, 1),
                "avg_cycle_time": round(avg_cycle_time, 1),
                "qualified_rate": round(qualified_rate, 1),
                "error_rate": round(error_rate, 1),
                "target_production": target_production,
                "actual_malfunctions": today_malfunctions,
                "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            return {"error": str(e)}
    
    def get_7day_production_trend(self, line_number: int = 1) -> Dict[str, Any]:
        """
        获取7天生产数量趋势
        """
        if not self.connection or not self.connection.is_connected():
            return {"error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)

            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=6)).strftime('%Y-%m-%d')

            # 查询最近7天的生产数据
            cursor.execute(f"""
                SELECT date_timestamp, COALESCE(SUM(count), 0) as production
                FROM line{line_number}_production_daily_count
                WHERE date_timestamp BETWEEN %s AND %s
                GROUP BY date_timestamp
                ORDER BY date_timestamp
            """, (start_date, end_date))

            results = cursor.fetchall()

            # 生成最近7天的日期列表
            dates = []
            production_counts = []
            for i in range(7):
                date = (datetime.now() - timedelta(days=6-i)).strftime('%Y-%m-%d')
                dates.append(date)

            # 整理数据，确保每天都有数据
            data_dict = {}
            for item in results:
                date_str = str(item['date_timestamp'])
                data_dict[date_str] = item['production']

            # 构建生产数量列表
            for date in dates:
                production_counts.append(data_dict.get(date, 0))

            cursor.close()

            return {
                "dates": dates,
                "production_counts": production_counts,
                "total_7days": sum(production_counts),
                "avg_daily": round(sum(production_counts) / 7, 1)
            }

        except Exception as e:
            return {"error": str(e)}
    
    def get_hourly_production(self, line_number: int = 1) -> Dict[str, Any]:
        """
        获取每小时生产数据（今日）
        """
        if not self.connection or not self.connection.is_connected():
            return {"error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)

            today = datetime.now().strftime('%Y-%m-%d')

            # 查询今日每小时生产数据
            cursor.execute(f"""
                SELECT hour_timestamp, COALESCE(SUM(count), 0) as production
                FROM line{line_number}_production_hourly_count
                WHERE DATE(hour_timestamp) = %s
                GROUP BY hour_timestamp
                ORDER BY hour_timestamp
            """, (today,))

            results = cursor.fetchall()

            # 生成今日24小时的时间列表
            hours = []
            production_counts = []
            for i in range(24):
                hour_time = datetime.now().replace(hour=i, minute=0, second=0, microsecond=0)
                hours.append(hour_time.strftime('%Y-%m-%d %H:00:00'))

            # 整理数据
            data_dict = {}
            for item in results:
                hour_str = str(item['hour_timestamp'])
                data_dict[hour_str] = item['production']

            # 构建生产数量列表
            current_hour = datetime.now().hour
            for i, hour in enumerate(hours):
                production = data_dict.get(hour, 0)
                production_counts.append(production)

            cursor.close()

            return {
                "hours": [f"{i:02d}:00" for i in range(24)],
                "production_counts": production_counts,
                "current_hour": current_hour,
                "current_hour_production": production_counts[current_hour] if current_hour < len(production_counts) else 0,
                "today_total": sum(production_counts)
            }

        except Exception as e:
            return {"error": str(e)}
    
    def get_quality_analysis(self, line_number: int = 1) -> Dict[str, Any]:
        """
        获取质量分析数据
        """
        if not self.connection or not self.connection.is_connected():
            return {"error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)

            # 获取最近7天的数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=6)).strftime('%Y-%m-%d')

            # 获取生产数据
            cursor.execute(f"""
                SELECT COALESCE(SUM(count), 0) as total_production
                FROM line{line_number}_production_daily_count
                WHERE date_timestamp BETWEEN %s AND %s
            """, (start_date, end_date))
            result = cursor.fetchone()
            total_production = result['total_production'] if result else 0

            # 获取故障数据
            cursor.execute(f"""
                SELECT COALESCE(SUM(count), 0) as total_malfunctions
                FROM line{line_number}_malfunction_daily_count
                WHERE date_timestamp BETWEEN %s AND %s
            """, (start_date, end_date))
            result = cursor.fetchone()
            total_malfunctions = result['total_malfunctions'] if result else 0

            # 计算质量指标
            total_products = total_production + total_malfunctions
            qualified_rate = (total_production / total_products * 100) if total_products > 0 else 100
            defect_rate = (total_malfunctions / total_products * 100) if total_products > 0 else 0

            cursor.close()

            return {
                "qualified_products": total_production,
                "defective_products": total_malfunctions,
                "total_products": total_products,
                "qualified_rate": round(qualified_rate, 2),
                "defect_rate": round(defect_rate, 2),
                "quality_grade": "优秀" if qualified_rate >= 95 else "良好" if qualified_rate >= 90 else "一般",
                "period": f"{start_date} 至 {end_date}"
            }

        except Exception as e:
            return {"error": str(e)}

    def get_hourly_efficiency(self, line_number: int = 1) -> Dict[str, Any]:
        """
        获取每小时生产效率数据
        """
        if not self.connection or not self.connection.is_connected():
            return {"error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)

            today = datetime.now().strftime('%Y-%m-%d')

            # 获取今日每小时生产数据
            cursor.execute(f"""
                SELECT hour_timestamp, COALESCE(SUM(count), 0) as production
                FROM line{line_number}_production_hourly_count
                WHERE DATE(hour_timestamp) = %s
                GROUP BY hour_timestamp
                ORDER BY hour_timestamp
            """, (today,))
            production_results = cursor.fetchall()

            # 整理生产数据
            production_dict = {}
            for item in production_results:
                hour_str = str(item['hour_timestamp'])
                production_dict[hour_str] = item['production']

            # 生成24小时效率数据
            hours = []
            production_counts = []
            efficiency_rates = []
            target_hourly = 42  # 假设每小时目标产量

            for i in range(24):
                hour_time = datetime.now().replace(hour=i, minute=0, second=0, microsecond=0)
                hour_str = hour_time.strftime('%Y-%m-%d %H:00:00')

                production = production_dict.get(hour_str, 0)
                efficiency = (production / target_hourly * 100) if target_hourly > 0 else 0

                hours.append(f"{i:02d}:00")
                production_counts.append(production)
                efficiency_rates.append(round(efficiency, 1))

            # 计算平均效率
            avg_efficiency = sum(efficiency_rates) / 24 if efficiency_rates else 0
            current_hour = datetime.now().hour
            current_hour_efficiency = efficiency_rates[current_hour] if current_hour < len(efficiency_rates) else 0

            cursor.close()

            return {
                "hours": hours,
                "production_counts": production_counts,
                "efficiency_rates": efficiency_rates,
                "target_hourly": target_hourly,
                "avg_efficiency": round(avg_efficiency, 1),
                "current_hour": current_hour,
                "current_hour_efficiency": current_hour_efficiency
            }

        except Exception as e:
            return {"error": str(e)}

    def get_malfunction_ranking(self, line_number: int = 1, days: int = 7) -> Dict[str, Any]:
        """
        获取坏机原因排行
        """
        if not self.connection or not self.connection.is_connected():
            return {"error": "数据库未连接"}

        try:
            cursor = self.connection.cursor(dictionary=True)

            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days-1)).strftime('%Y-%m-%d')

            # 按故障类型统计
            cursor.execute(f"""
                SELECT
                    malfunction_type,
                    SUM(count) as total_count,
                    COUNT(*) as occurrence_days,
                    AVG(count) as avg_daily_count,
                    MAX(count) as max_daily_count,
                    MIN(count) as min_daily_count
                FROM line{line_number}_malfunction_daily_count
                WHERE date_timestamp BETWEEN %s AND %s
                GROUP BY malfunction_type
                ORDER BY total_count DESC
            """, (start_date, end_date))

            malfunction_stats = cursor.fetchall()

            # 获取故障类型信息
            cursor.execute(f"SELECT type_code, type_name FROM line{line_number}_malfunction_type_info")
            type_info_results = cursor.fetchall()
            type_info = {row['type_code']: row['type_name'] for row in type_info_results}

            # 计算总故障数
            total_malfunctions = sum(stat['total_count'] for stat in malfunction_stats)

            # 构建排行数据
            malfunction_types = []
            malfunction_counts = []
            malfunction_percentages = []
            occurrence_days = []
            avg_daily_counts = []

            for stat in malfunction_stats[:10]:  # 取前10名
                percentage = (stat['total_count'] / total_malfunctions * 100) if total_malfunctions > 0 else 0
                malfunction_types.append(type_info.get(stat['malfunction_type'], '未知'))
                malfunction_counts.append(stat['total_count'])
                malfunction_percentages.append(round(percentage, 1))
                occurrence_days.append(stat['occurrence_days'])
                avg_daily_counts.append(round(stat['avg_daily_count'], 1))

            cursor.close()

            return {
                "malfunction_types": malfunction_types,
                "malfunction_counts": malfunction_counts,
                "malfunction_percentages": malfunction_percentages,
                "occurrence_days": occurrence_days,
                "avg_daily_counts": avg_daily_counts,
                "total_malfunctions": total_malfunctions,
                "period_days": days,
                "period": f"{start_date} 至 {end_date}",
                "top_malfunction": malfunction_types[0] if malfunction_types else "无"
            }

        except Exception as e:
            return {"error": str(e)}

    def get_all_dashboard_data(self, line_number: int = 1) -> Dict[str, Any]:
        """
        获取所有看板数据（一次性获取所有模块数据）
        """
        try:
            overview = self.get_production_overview(line_number)
            trend_7days = self.get_7day_production_trend(line_number)
            hourly_production = self.get_hourly_production(line_number)
            quality_analysis = self.get_quality_analysis(line_number)
            hourly_efficiency = self.get_hourly_efficiency(line_number)
            malfunction_ranking = self.get_malfunction_ranking(line_number)

            # 合并所有数据到一层对象
            result = {
                "line_number": line_number,
                "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 添加各模块数据，如果有错误则跳过
            if "error" not in overview:
                result.update({k: v for k, v in overview.items() if k != "error"})
            if "error" not in trend_7days:
                result.update({f"trend_{k}": v for k, v in trend_7days.items() if k != "error"})
            if "error" not in hourly_production:
                result.update({f"hourly_{k}": v for k, v in hourly_production.items() if k != "error"})
            if "error" not in quality_analysis:
                result.update({f"quality_{k}": v for k, v in quality_analysis.items() if k != "error"})
            if "error" not in hourly_efficiency:
                result.update({f"efficiency_{k}": v for k, v in hourly_efficiency.items() if k != "error"})
            if "error" not in malfunction_ranking:
                result.update({f"ranking_{k}": v for k, v in malfunction_ranking.items() if k != "error"})

            return result

        except Exception as e:
            return {"error": str(e)}


# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': '20016f7d55f95fb9',
    'database': 'production_data'
}


def create_dashboard_api():
    """创建看板API实例"""
    return DashboardDataAPI(DB_CONFIG)


# Flask API 路由
if __name__ == '__main__':
    from flask import Flask, jsonify, request

    app = Flask(__name__)

    @app.route('/api/dashboard/overview/<int:line_number>', methods=['GET'])
    def get_overview(line_number):
        """获取生产概览"""
        api = create_dashboard_api()
        try:
            result = api.get_production_overview(line_number)
            return jsonify(result)
        finally:
            api.disconnect()

    @app.route('/api/dashboard/trend/<int:line_number>', methods=['GET'])
    def get_trend(line_number):
        """获取7天趋势"""
        api = create_dashboard_api()
        try:
            result = api.get_7day_production_trend(line_number)
            return jsonify(result)
        finally:
            api.disconnect()

    @app.route('/api/dashboard/hourly/<int:line_number>', methods=['GET'])
    def get_hourly(line_number):
        """获取每小时数据"""
        api = create_dashboard_api()
        try:
            result = api.get_hourly_production(line_number)
            return jsonify(result)
        finally:
            api.disconnect()

    @app.route('/api/dashboard/quality/<int:line_number>', methods=['GET'])
    def get_quality(line_number):
        """获取质量分析"""
        api = create_dashboard_api()
        try:
            result = api.get_quality_analysis(line_number)
            return jsonify(result)
        finally:
            api.disconnect()

    @app.route('/api/dashboard/efficiency/<int:line_number>', methods=['GET'])
    def get_efficiency(line_number):
        """获取每小时效率"""
        api = create_dashboard_api()
        try:
            result = api.get_hourly_efficiency(line_number)
            return jsonify(result)
        finally:
            api.disconnect()

    @app.route('/api/dashboard/malfunction/<int:line_number>', methods=['GET'])
    def get_malfunction(line_number):
        """获取故障排行"""
        api = create_dashboard_api()
        try:
            days = request.args.get('days', 7, type=int)
            result = api.get_malfunction_ranking(line_number, days)
            return jsonify(result)
        finally:
            api.disconnect()

    @app.route('/api/dashboard/all/<int:line_number>', methods=['GET'])
    def get_all_data(line_number):
        """获取所有看板数据"""
        api = create_dashboard_api()
        try:
            result = api.get_all_dashboard_data(line_number)
            return jsonify(result)
        finally:
            api.disconnect()

    print("=== 生产看板数据API ===")
    print("API接口:")
    print("GET /api/dashboard/overview/1    - 生产概览总览")
    print("GET /api/dashboard/trend/1       - 7天生产趋势")
    print("GET /api/dashboard/hourly/1      - 每小时生产数据")
    print("GET /api/dashboard/quality/1     - 质量分析")
    print("GET /api/dashboard/efficiency/1  - 每小时效率")
    print("GET /api/dashboard/malfunction/1 - 坏机原因排行")
    print("GET /api/dashboard/all/1         - 所有看板数据")
    print("")
    print("服务器地址: http://*************:5001")
    print("========================")

    app.run(host='0.0.0.0', port=5001, debug=True)
