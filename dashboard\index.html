<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>飞控子工序生产看板</title>
  <script src="http://*************:50401/dv/d66227b32198acbeca55/js/chart.js"></script>
  <style>
    :root{
      --primary-color:#00f2fe;--secondary-color:#4facfe;--accent-color:#00ff9d;
      --dark-bg:#0f172a;--panel-bg:rgba(15,23,42,.7);--text-color:#e2e8f0;--text-muted:#94a3b8
    }
    *{margin:0;padding:0;box-sizing:border-box;font-family:Arial,system-ui,-apple-system,Segoe UI,Roboto,sans-serif}
    body{background:var(--dark-bg);color:var(--text-color);overflow:hidden;height:100vh;width:100vw;
      background-image:radial-gradient(circle at 10% 20%,rgba(0,242,254,.1) 0%,transparent 20%),
      radial-gradient(circle at 90% 80%,rgba(79,172,254,.1) 0%,transparent 20%)}
    .dashboard{width:100%;height:100%;padding:1.5vmin;display:grid;
      grid-template-columns:1.2fr .8fr 1fr;grid-template-rows:8vh 1fr 1fr;gap:1.5vmin;position:relative}
    .header{grid-column:1/4;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color));
      color:var(--dark-bg);display:flex;align-items:center;justify-content:center;border-radius:1vmin;
      box-shadow:0 0 2vmin rgba(0,242,254,.3);position:relative;overflow:hidden;z-index:1}
    .header::before{content:"";position:absolute;top:-50%;left:-50%;width:200%;height:200%;
      background:linear-gradient(to bottom right,rgba(255,255,255,.3) 0%,rgba(255,255,255,0) 60%);
      transform:rotate(30deg);z-index:-1}
    .header h1{font-size:3.5vmin;font-weight:700;letter-spacing:.5vmin;text-transform:uppercase}
    .time-display{position:absolute;top:50%;right:3vmin;transform:translateY(-50%);
      background:rgba(15,23,42,.7);padding:1vmin 2vmin;border-radius:2vmin;font-size:2vmin;
      box-shadow:0 0 1vmin rgba(0,0,0,.3);border:1px solid rgba(79,172,254,.3);display:flex;align-items:center}
    .time-display::before{content:"";display:inline-block;width:1.5vmin;height:1.5vmin;background:var(--accent-color);
      border-radius:50%;margin-right:1vmin;box-shadow:0 0 1vmin var(--accent-color)}
    .panel{background:var(--panel-bg);border-radius:1.5vmin;padding:2vmin;box-shadow:0 0 1.5vmin rgba(0,0,0,.3);
      display:flex;flex-direction:column;border:1px solid rgba(79,172,254,.2);position:relative;overflow:hidden}
    .panel::after{content:"";position:absolute;top:0;left:0;right:0;height:.3vmin;
      background:linear-gradient(90deg,var(--primary-color),var(--secondary-color))}
    .panel-title{font-size:2.2vmin;font-weight:700;margin-bottom:1.2vmin;color:var(--primary-color);display:flex;align-items:center}
    .panel-title::before{content:"";display:inline-block;width:.8vmin;height:2vmin;background:var(--accent-color);
      margin-right:1vmin;border-radius:.4vmin}
    .data-table{width:100%;border-collapse:collapse;flex-grow:1;font-size:1.8vmin}
    .data-table th,.data-table td{border:1px solid rgba(79,172,254,.2);padding:1.2vmin;text-align:center}
    .data-table th{background:rgba(79,172,254,.1);font-weight:700;color:var(--primary-color)}
    .data-table tr:nth-child(even){background:rgba(15,23,42,.5)}
    .data-table tr:hover{background:rgba(79,172,254,.1)}
    .chart-container{position:relative;height:calc(100% - 4vmin);width:100%}
    .kpi-container{display:grid;grid-template-columns:1fr 1fr;grid-template-rows:1fr 1fr;gap:1.2vmin;margin-top:1.2vmin}
    .kpi-item{background:rgba(15,23,42,.7);border-radius:1vmin;padding:1.8vmin;display:flex;flex-direction:column;align-items:center;
      justify-content:center;border:1px solid rgba(79,172,254,.2);box-shadow:0 0 1vmin rgba(0,0,0,.2);position:relative}
    .kpi-item::before{content:"";position:absolute;bottom:0;left:0;right:0;height:.3vmin;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color))}
    .kpi-label{font-size:1.7vmin;color:var(--text-muted);margin-bottom:.8vmin}
    .kpi-value{font-size:3.2vmin;font-weight:700;color:var(--primary-color)}
    .positive{color:var(--accent-color)} .negative{color:#ff6b6b}
    .glow-effect{position:absolute;width:100%;height:100%;top:0;left:0;background:radial-gradient(circle at center,rgba(0,242,254,.1) 0%,transparent 70%);pointer-events:none;z-index:-1}
    .watermark{position:absolute;left:2vmin;bottom:2vmin;font-size:1.8vmin;color:rgba(148,163,184,.6);z-index:10}
    /* 坏机原因排行 */
    .reason-container{margin-top:1vmin;display:flex;flex-direction:column;gap:.4vmin}
    .reason-row{display:grid;grid-template-columns:3vmin 1fr auto auto;gap:1vmin;align-items:center;padding:.6vmin .2vmin;border-bottom:1px solid rgba(79,172,254,.08)}
    .reason-rank{width:3vmin;height:3vmin;border-radius:50%;display:flex;align-items:center;justify-content:center;
      font-size:1.6vmin;font-weight:700;color:#ff6b6b;background:rgba(255,107,107,.25)}
    .reason-name{font-size:1.8vmin;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
    .reason-count{font-size:1.8vmin;font-weight:700;color:#ff6b6b;background:rgba(15,23,42,.7);padding:.3vmin 1vmin;border-radius:2vmin;border:1px solid rgba(255,107,107,.35)}
    .reason-percent{font-size:1.6vmin;color:#94a3b8;text-align:right;width:6vmin}
  </style>
</head>
<body>
  <div class="dashboard">
    <div class="watermark">广东科贸吴菀毓，胡江飞制</div>

    <div class="header">
      <h1>飞控子工序生产看板</h1>
      <div class="time-display" id="current-time"></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">生产数据总览</div>
      <table class="data-table" id="summary-table">
        <thead>
        <tr>
          <th>产品编号</th>
          <th>总生产数量</th>
          <th>生产效率</th>
          <th>平均生产时间</th>
          <th>合格数</th>
          <th>错误数</th>
        </tr>
        </thead>
        <tbody></tbody>
      </table>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">7天生产数量趋势</div>
      <div class="chart-container"><canvas id="weeklyProductionChart"></canvas></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">每小时生产数据</div>
      <div class="chart-container"><canvas id="hourlyProductionChart"></canvas></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">质量分析</div>
      <div class="kpi-container" id="kpi-cards"></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">每小时生产效率</div>
      <div class="chart-container"><canvas id="efficiencyChart"></canvas></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">坏机原因排行</div>
      <div class="chart-container"><canvas id="errorReasonChart"></canvas></div>
      <div class="reason-container" id="reason-list"></div>
      <div class="glow-effect"></div>
    </div>
  </div>

<script>
(() => {
  'use strict';

  // ===== 全局 Chart 样式 =====
  Chart.defaults.color = '#e2e8f0';
  Chart.defaults.borderColor = 'rgba(79,172,254,0.2)';

  // ===== 统一数值标签插件（复用，避免多次注册）=====
  const ValueLabelsPlugin = {
    id: 'valueLabels',
    afterDatasetsDraw(chart, args, opts) {
      const cfg = Object.assign({ fontSize: 12, color: '#fff', offset: 4, formatter: v => v }, opts || {});
      const ctx = chart.ctx;
      ctx.save();
      ctx.font = `bold ${cfg.fontSize}px Arial`;
      ctx.fillStyle = cfg.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';

      chart.data.datasets.forEach((ds, i) => {
        const meta = chart.getDatasetMeta(i);
        if (!meta || meta.hidden) return;
        meta.data.forEach((el, idx) => {
          const val = ds.data[idx];
          if (val == null) return;
          const p = el.tooltipPosition ? el.tooltipPosition() : { x: el.x, y: el.y };
          const text = cfg.formatter(val, ds, idx);
          ctx.fillText(text, p.x, p.y - cfg.offset);
        });
      });
      ctx.restore();
    }
  };
  Chart.register(ValueLabelsPlugin);

  // ===== 工具：时间与随机 =====
  const $ = sel => document.querySelector(sel);
  let clockTimer = null;
  let dataRefreshTimer = null;

  function startClock() {
    const el = $('#current-time');
    const tick = () => {
      const now = new Date();
      const pad = n => String(n).padStart(2, '0');
      const s = `${now.getFullYear()}-${pad(now.getMonth()+1)}-${pad(now.getDate())} `
              + `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;
      el.textContent = s;
    };
    tick();
    clockTimer = setInterval(tick, 1000);
  }

  function stopClock() {
    if (clockTimer) { clearInterval(clockTimer); clockTimer = null; }
  }

  function randInt(min, max) { return Math.floor(Math.random()*(max-min+1))+min; }
  function clamp(n, a, b){ return Math.max(a, Math.min(b, n)); }

  // ===== API数据获取 =====
  const API_BASE_URL = 'http://*************:5000/api';
  
  async function fetchData(endpoint) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`);
      if (!response.ok) {
        throw new Error(`API错误: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`获取数据失败: ${error.message}`);
      return null;
    }
  }

  async function fetchAllData() {
    try {
      // 检查API是否可用
      const apiAvailable = true; // 设置为true以尝试连接API
      
      if (apiAvailable) {
        // 并行获取所有数据
        const [current, hourly, daily, recentHourly, customDaily] = await Promise.all([
          fetchData('/stats/current'),
          fetchData('/stats/hourly'),
          fetchData('/stats/daily'),
          fetchData('/stats/hourly/recent'),
          fetchData('/stats/daily/custom?days=7')
        ]);

        // 处理数据并转换为仪表盘所需格式
        return processApiData(current, hourly, daily, recentHourly, customDaily);
      } else {
        console.log('使用模拟数据模式');
        return genMockData();
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      console.log('切换到模拟数据模式');
      return genMockData();
    }
  }

  function processApiData(current, hourly, daily, recentHourly, customDaily) {
    // 如果API数据获取失败，返回模拟数据
    if (!current || !hourly || !daily || !recentHourly || !customDaily) {
      console.warn('使用模拟数据');
      return genMockData();
    }

    // 处理每日数据
    const dailyLabels = customDaily.map(item => {
      const date = new Date(item.date_timestamp);
      return `${String(date.getMonth()+1).padStart(2,'0')}/${String(date.getDate()).padStart(2,'0')}`;
    });
    const dailyData = customDaily.map(item => item.count);

    // 处理每小时数据
    const hourlyLabels = recentHourly.map(item => {
      const date = new Date(item.hour_timestamp);
      return `${date.getHours()}:00`;
    });
    const hourlyData = recentHourly.map(item => item.count);
    
    // 从API获取错误数据，如果没有则使用空数组
    const errorData = recentHourly.map(item => item.error_count || 0);

    // 模拟效率数据（实际项目中应从API获取）
    const effData = hourlyLabels.map(() => clamp(90 + randInt(-3, 3), 86, 98));

    // 计算KPI
    const totalCount = current ? current.total_count : 0;
    const avgEff = (effData.reduce((a,b) => a+b, 0) / effData.length).toFixed(1);
    const pass = "95.2"; // 模拟数据，实际应从API获取
    const ng = (100 - parseFloat(pass)).toFixed(1);
    const avgTime = "2.3"; // 模拟数据，实际应从API获取

    // 生成总览表数据
    const summary = [
      {id:'生产线1', total: totalCount}
    ].map(row => {
      const effp = avgEff + '%';
      const ok = Math.round(row.total * (parseFloat(pass)/100));
      const bad = row.total - ok;
      return { id: row.id, total: row.total, eff: effp, avg: avgTime+'分钟', ok, bad };
    });

    // 从API获取坏机原因，如果没有则使用空数组
    let reasons = [];
    if (current && current.error_reasons) {
      reasons = current.error_reasons;
    }
    
    // 如果没有错误原因数据，则不计算百分比
    if (reasons.length > 0) {
      const totalBad = reasons.reduce((a,b) => a+b.count, 0);
      reasons.forEach(r => r.percent = totalBad > 0 ? Math.round(100*r.count/totalBad) : 0);
    }

    return {
      weekly: { labels: dailyLabels, data: dailyData },
      hourly: { labels: hourlyLabels, prod: hourlyData, err: errorData },
      eff: { labels: hourlyLabels, data: effData },
      kpi: { pass, ng, avgEff, avgTime },
      summary,
      reasons
    };
  }

  // ===== 模拟数据生成（备用）=====
  function genMockData() {
    // 7天
    const today = new Date();
    const labels7 = [...Array(7)].map((_,i)=>{
      const d = new Date(today); d.setDate(today.getDate()-6+i);
      const mm = String(d.getMonth()+1).padStart(2,'0');
      const dd = String(d.getDate()).padStart(2,'0');
      return `${mm}/${dd}`;
    });
    const weekly = labels7.map(()=>randInt(900, 1600));

    // 每小时（8~17点共10个）
    const hours = Array.from({length:10},(_,i)=>`${8+i}:00`);
    const prodPerHour = hours.map((_,i)=> clamp(120 + (i-4)*8 + randInt(-15,15), 70, 190));
    // 不模拟错误数据
    const errPerHour = new Array(hours.length).fill(0);

    // 效率（%）
    const eff = hours.map((_,i)=> clamp(90 + (i-5)*0.8 + randInt(-2,2), 86, 98));

    // KPI
    const pass = clamp(95 - randInt(0,3)/10, 90, 99).toFixed(1);
    const ng   = (100 - pass).toFixed(1);
    const avgEff = (eff.reduce((a,b)=>a+b,0)/eff.length).toFixed(1);
    const avgTime = (2.0 + randInt(-5,8)/10).toFixed(1); // 分钟

    // 总览表（4 个产品）
    const summary = [
      {id:'PROD-2025-001', total: randInt(900,1600)},
      {id:'PROD-2025-002', total: randInt(800,1300)},
      {id:'PROD-2025-003', total: randInt(1100,1700)},
      {id:'PROD-2025-004', total: randInt(700,1200)}
    ].map(row=>{
      const effp = clamp(randInt(885, 960)/10, 85, 98).toFixed(1);
      const ok = Math.round(row.total * (effp/100));
      const bad = row.total - ok;
      const avg = (1.6 + Math.random()*1.8).toFixed(1);
      return { id: row.id, total: row.total, eff: effp+'%', avg: avg+'分钟', ok, bad };
    });

    // 不模拟坏机原因
    const reasons = [];

    return {
      weekly: { labels: labels7, data: weekly },
      hourly: { labels: hours, prod: prodPerHour, err: errPerHour },
      eff:    { labels: hours, data: eff },
      kpi:    { pass, ng, avgEff, avgTime },
      summary,
      reasons
    };
  }

  // ===== App：统一创建/更新/释放 =====
  const App = {
    charts: { weekly:null, hourly:null, eff:null, reason:null },
    listeners: [],
    init() {
      startClock();
      this.buildCharts();
      
      // 初始加载数据
      this.loadRealData();
      
      // 设置定时刷新（每60秒）
      dataRefreshTimer = setInterval(() => this.loadRealData(), 60000);
      
      // 管理事件监听（便于释放）
      const onHide = () => { if (document.hidden) stopClock(); else startClock(); };
      const onUnload = () => this.dispose();
      document.addEventListener('visibilitychange', onHide);
      window.addEventListener('beforeunload', onUnload);
      this.listeners.push(['visibilitychange', onHide, document], ['beforeunload', onUnload, window]);
    },
    
    async loadRealData() {
      try {
        const data = await fetchAllData();
        if (data) {
          this.fillSummary(data.summary);
          this.fillKpis(data.kpi);
          this.updateWeekly(data.weekly);
          this.updateHourly(data.hourly);
          this.updateEff(data.eff);
          this.updateReasons(data.reasons);
          console.log('数据已更新:', new Date().toLocaleTimeString());
        } else {
          // 如果API数据获取失败，使用模拟数据
          this.renderMock();
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        this.renderMock();
      }
    },
    
    renderMock() {
      console.warn('使用模拟数据');
      const mock = genMockData();
      this.fillSummary(mock.summary);
      this.fillKpis(mock.kpi);
      this.updateWeekly(mock.weekly);
      this.updateHourly(mock.hourly);
      this.updateEff(mock.eff);
      this.updateReasons(mock.reasons);
    },
    setData(payload){
      // 供你后续塞真实数据：任意字段可选
      if (payload.summary) this.fillSummary(payload.summary);
      if (payload.kpi) this.fillKpis(payload.kpi);
      if (payload.weekly) this.updateWeekly(payload.weekly);
      if (payload.hourly) this.updateHourly(payload.hourly);
      if (payload.eff) this.updateEff(payload.eff);
      if (payload.reasons) this.updateReasons(payload.reasons);
    },
    fillSummary(rows){
      const tbody = document.querySelector('#summary-table tbody');
      tbody.innerHTML = '';
      const frag = document.createDocumentFragment();
      rows.forEach(r=>{
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${r.id}</td>
          <td class="data-highlight">${r.total.toLocaleString()}</td>
          <td class="positive">${r.eff}</td>
          <td>${r.avg}</td>
          <td class="positive">${Number(r.ok).toLocaleString()}</td>
          <td class="negative">${Number(r.bad).toLocaleString()}</td>`;
        frag.appendChild(tr);
      });
      tbody.appendChild(frag);
    },
    fillKpis(k){
      const el = document.getElementById('kpi-cards');
      el.innerHTML = `
        <div class="kpi-item"><div class="kpi-label">今日PASS率</div><div class="kpi-value positive">${k.pass}%</div></div>
        <div class="kpi-item"><div class="kpi-label">今日NG率</div><div class="kpi-value negative">${k.ng}%</div></div>
        <div class="kpi-item"><div class="kpi-label">平均生产效率</div><div class="kpi-value positive">${k.avgEff}%</div></div>
        <div class="kpi-item"><div class="kpi-label">平均生产时间</div><div class="kpi-value">${k.avgTime}</div></div>`;
    },
    buildCharts(){
      // 7天
      this.charts.weekly = new Chart(document.getElementById('weeklyProductionChart').getContext('2d'), {
        type:'bar',
        data:{ labels:[], datasets:[{ label:'生产数量', data:[],
          backgroundColor:'rgba(0,242,254,0.7)', borderColor:'rgba(0,242,254,1)', borderWidth:1, borderRadius:4 }]},
        options:{
          responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:true, position:'top' }, valueLabels:{ fontSize:12, formatter:v=>v } },
          scales:{ y:{ beginAtZero:true, grid:{ color:'rgba(79,172,254,0.1)' }, title:{ display:true, text:'生产数量' }},
                   x:{ grid:{ color:'rgba(79,172,254,0.1)' }, reverse: true } }
        }
      });

      // 每小时生产/错误
      this.charts.hourly = new Chart(document.getElementById('hourlyProductionChart').getContext('2d'), {
        type:'line',
        data:{ labels:[], datasets:[
          { label:'生产数量', data:[], fill:true, tension:.3, borderWidth:2,
            backgroundColor:'rgba(0,242,254,0.2)', borderColor:'rgba(0,242,254,1)', yAxisID:'y' },
          { label:'错误数', data:[], fill:false, tension:.3, borderWidth:2,
            backgroundColor:'rgba(255,107,107,0.2)', borderColor:'rgba(255,107,107,1)', yAxisID:'y1' }
        ]},
        options:{
          responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:true, position:'top' }, valueLabels:{ fontSize:11, formatter:v=>v } },
          scales:{ y:{ position:'left', grid:{ color:'rgba(79,172,254,0.1)'} , title:{display:true,text:'生产数量'} },
                   y1:{ position:'right', grid:{ drawOnChartArea:false }, title:{display:true,text:'错误数'} },
                   x:{ grid:{ color:'rgba(79,172,254,0.1)'}, reverse: true } }
        }
      });

      // 每小时效率
      this.charts.eff = new Chart(document.getElementById('efficiencyChart').getContext('2d'), {
        type:'line',
        data:{ labels:[], datasets:[
          { label:'生产效率 (%)', data:[], fill:true, tension:.3, borderWidth:2,
            backgroundColor:'rgba(0,255,157,0.2)', borderColor:'rgba(0,255,157,1)' }
        ]},
        options:{
          responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:true, position:'top' }, valueLabels:{ fontSize:11, formatter:v=>`${v}%` } },
          scales:{ y:{ min:85, max:100, grid:{ color:'rgba(79,172,254,0.1)'}, title:{display:true,text:'生产效率 (%)'}},
                   x:{ grid:{ color:'rgba(79,172,254,0.1)'}, reverse: true } }
        }
      });

      // 坏机原因（横向）
      this.charts.reason = new Chart(document.getElementById('errorReasonChart').getContext('2d'), {
        type:'bar',
        data:{ labels:[], datasets:[{ label:'坏机次数', data:[],
          backgroundColor:[
            'rgba(255,99,132,0.7)','rgba(255,159,64,0.7)','rgba(255,205,86,0.7)','rgba(75,192,192,0.7)','rgba(54,162,235,0.7)'
          ], borderColor:[
            'rgba(255,99,132,1)','rgba(255,159,64,1)','rgba(255,205,86,1)','rgba(75,192,192,1)','rgba(54,162,235,1)'
          ], borderWidth:1, borderRadius:4 }]},
        options:{
          indexAxis:'y', responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:false }, valueLabels:{ fontSize:12, formatter:(v,ds,i)=>{
            const total = ds.data.reduce((a,b)=>a+b,0)||1; const p = Math.round(v*100/total);
            return `${v} (${p}%)`;
          }}},
          scales:{ x:{ beginAtZero:true, grid:{ color:'rgba(79,172,254,0.1)'}, title:{display:true,text:'坏机次数'} },
                   y:{ grid:{ color:'rgba(79,172,254,0.1)'} } }
        }
      });
    },
    updateWeekly({labels, data}){
      const c = this.charts.weekly;
      c.data.labels = labels.slice();
      c.data.datasets[0].data = data.slice();
      c.update('none');
    },
    updateHourly({labels, prod, err}){
      const c = this.charts.hourly;
      c.data.labels = labels.slice();
      c.data.datasets[0].data = prod.slice();
      c.data.datasets[1].data = err.slice();
      c.update('none');
    },
    updateEff({labels, data}){
      const c = this.charts.eff;
      c.data.labels = labels.slice();
      c.data.datasets[0].data = data.slice();
      c.update('none');
    },
    updateReasons(list){
      const c = this.charts.reason;
      c.data.labels = list.map(r=>r.name);
      c.data.datasets[0].data = list.map(r=>r.count);
      c.update('none');
      // 文本列表
      const box = document.getElementById('reason-list');
      box.innerHTML = '';
      const frag = document.createDocumentFragment();
      box.appendChild(frag);
    },
    dispose(){
      // 清理时钟
      stopClock();
      // 移除监听
      this.listeners.forEach(([evt, fn, target]) => (target||document).removeEventListener(evt, fn));
      this.listeners = [];
      // 销毁图表释放内存
      Object.keys(this.charts).forEach(k=>{
        const inst = this.charts[k];
        if (inst) { try { inst.clear(); inst.destroy(); } catch(e){} this.charts[k]=null; }
      });
    }
  };

  // 暴露给外部（便于你后续 setData / 手动释放）
  window.DashboardApp = App;

  // 启动
  App.init();

})();
</script>
</body>
</html>